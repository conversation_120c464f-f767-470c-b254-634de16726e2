/*!
 * jsoneditor.js
 *
 * @brief
 * JSONEditor is a web-based tool to view, edit, format, and validate JSON.
 * It has various modes such as a tree editor, a code editor, and a plain text
 * editor.
 *
 * Supported browsers: Chrome, Firefox, Safari, Opera, Internet Explorer 8+
 *
 * @license
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy
 * of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 *
 * Copyright (c) 2011-2020 Jo<PERSON>, http://jsoneditoronline.org
 *
 * <AUTHOR> <<EMAIL>>
 * @version 9.0.3
 * @date    2020-07-06
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.JSONEditor=t():e.JSONEditor=t()}(window,function(){return i={},o.m=n=[function(e,t,n){"use strict";n.r(t),n.d(t,"parse",function(){return p}),n.d(t,"repair",function(){return f}),n.d(t,"escapeUnicodeChars",function(){return m}),n.d(t,"validate",function(){return v}),n.d(t,"extend",function(){return g}),n.d(t,"clear",function(){return y}),n.d(t,"getType",function(){return b}),n.d(t,"isUrl",function(){return C}),n.d(t,"isArray",function(){return _}),n.d(t,"getAbsoluteLeft",function(){return E}),n.d(t,"getAbsoluteTop",function(){return w}),n.d(t,"addClassName",function(){return T}),n.d(t,"removeAllClassNames",function(){return j}),n.d(t,"removeClassName",function(){return S}),n.d(t,"stripFormatting",function(){return N}),n.d(t,"setEndOfContentEditable",function(){return k}),n.d(t,"selectContentEditable",function(){return O}),n.d(t,"getSelection",function(){return I}),n.d(t,"setSelection",function(){return D}),n.d(t,"getSelectionOffset",function(){return A}),n.d(t,"setSelectionOffset",function(){return P}),n.d(t,"getInnerText",function(){return F}),n.d(t,"hasParentNode",function(){return M}),n.d(t,"getInternetExplorerVersion",function(){return V}),n.d(t,"isFirefox",function(){return B}),n.d(t,"addEventListener",function(){return R}),n.d(t,"removeEventListener",function(){return z}),n.d(t,"isChildOf",function(){return H}),n.d(t,"parsePath",function(){return U}),n.d(t,"stringifyPath",function(){return J}),n.d(t,"improveSchemaError",function(){return $}),n.d(t,"isPromise",function(){return q}),n.d(t,"isValidValidationError",function(){return K}),n.d(t,"insideRect",function(){return Q}),n.d(t,"debounce",function(){return W}),n.d(t,"textDiff",function(){return X}),n.d(t,"getInputSelection",function(){return Z}),n.d(t,"getIndexForPosition",function(){return Y}),n.d(t,"getPositionForPath",function(){return G}),n.d(t,"compileJSONPointer",function(){return ee}),n.d(t,"getColorCSS",function(){return te}),n.d(t,"isValidColor",function(){return ne}),n.d(t,"makeFieldTooltip",function(){return ie}),n.d(t,"get",function(){return oe}),n.d(t,"findUniqueName",function(){return re}),n.d(t,"getChildPaths",function(){return se}),n.d(t,"sort",function(){return ae}),n.d(t,"sortObjectKeys",function(){return le}),n.d(t,"parseString",function(){return ce}),n.d(t,"isTimestamp",function(){return de}),n.d(t,"formatSize",function(){return he}),n.d(t,"limitCharacters",function(){return ue}),n.d(t,"isObject",function(){return pe}),n.d(t,"contains",function(){return fe}),n.d(t,"isValidationErrorChanged",function(){return me});n(18);var i=n(10),r=n.n(i),o=n(17),s=n.n(o),a=n(19),l=n.n(a),c=n(1);function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var h=1e4,u=9466848e5;function p(t){try{return JSON.parse(t)}catch(e){throw v(t),e}}function f(i){var n=[],o=0,e=0,t=!1,r=i.match(/^\s*(\/\*(.|[\r\n])*?\*\/)?\s*[\da-zA-Z_$]+\s*\(([\s\S]*)\)\s*;?\s*$/);r&&(i=r[3]);var s,a={"\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t"},l={None:"null",True:"true",False:"false"};function c(){return i.charAt(o)}function d(){return i.charAt(o+1)}function h(e){return" "===e||"\n"===e||"\r"===e||"\t"===e}function u(){if("/"===c()&&"*"===d()){for(o+=2;o<i.length&&("*"!==c()||"/"!==d());)o++;o+=2,"\n"===c()&&o++}}function p(e){var t="";t+='"',o++;for(var n=c();o<i.length&&n!==e;)'"'===n&&"\\"!==i.charAt(o-1)?t+='\\"':n in a?t+=a[n]:("\\"===n&&(o++,"'"!==(n=c())&&(t+="\\")),t+=n),o++,n=c();return n===e&&(t+='"',o++),t}for(;o<i.length;){u(),function(){if("/"===c()&&"/"===d())for(o+=2;o<i.length&&"\n"!==c();)o++}();var f,m=c();"{"===m&&e++,"}"===m&&e--," "===(s=m)||" "<=s&&s<=" "||" "===s||" "===s||"　"===s?(n.push(" "),o++):"'"===m?n.push(p(m)):'"'===m?n.push(p('"')):"`"===m?n.push(p("´")):"‘"===m?n.push(p("’")):"“"===m?n.push(p("”")):"}"===m?(n.push(m),o++,f=function(){for(var e="";o<i.length&&h(c());)e+=c(),o++;return e}(),u(),"{"===function(){for(var e=o;e<i.length&&h(i[e]);)e++;return i[e]}()&&(n.push(","),0===e&&(t=!0)),n.push(f)):","===m&&-1!==["]","}"].indexOf(function(){for(var e=o+1;e<i.length&&h(i[e]);)e++;return i[e]}())?o++:/[a-zA-Z_$]/.test(m)&&-1!==["{",","].indexOf(function(){for(var e=n.length-1;0<=e;){var t=n[e];if(!h(t))return t;e--}return""}())?n.push(function(){for(var e="",t=c(),n=/[a-zA-Z_$\d]/;n.test(t);)e+=t,o++,t=c();return e in l?l[e]:-1===["null","true","false"].indexOf(e)?'"'+e+'"':e}()):/\w/.test(m)?n.push(function(){for(var e,t=c(),n="";/\w/.test(t);)n+=t,o++,t=c();if(0<n.length&&"("===t){if(o++,'"'===(t=c()))e=p(t),t=c();else for(e="";")"!==t&&""!==t;)e+=t,o++,t=c();return")"===t?(o++,e):n+"("+e+t}return"string"==typeof l[n]?l[n]:n}()):(n.push(m),o++)}return t&&(n.unshift("[\n"),n.push("\n]")),n.join("")}function m(e){return e.replace(/[\u007F-\uFFFF]/g,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})}function v(e){void 0!==s.a?s.a.parse(e):JSON.parse(e)}function g(e,t){for(var n in t)ve(t,n)&&(e[n]=t[n]);return e}function y(e){for(var t in e)ve(e,t)&&delete e[t];return e}function b(e){return null===e?"null":void 0===e?"undefined":e instanceof Number||"number"==typeof e?"number":e instanceof String||"string"==typeof e?"string":e instanceof Boolean||"boolean"==typeof e?"boolean":e instanceof RegExp?"regexp":_(e)?"array":"object"}var x=/^https?:\/\/\S+$/;function C(e){return("string"==typeof e||e instanceof String)&&x.test(e)}function _(e){return"[object Array]"===Object.prototype.toString.call(e)}function E(e){return e.getBoundingClientRect().left+window.pageXOffset||document.scrollLeft||0}function w(e){return e.getBoundingClientRect().top+window.pageYOffset||document.scrollTop||0}function T(e,t){var n=e.className.split(" ");-1===n.indexOf(t)&&(n.push(t),e.className=n.join(" "))}function j(e){e.className=""}function S(e,t){var n=e.className.split(" "),i=n.indexOf(t);-1!==i&&(n.splice(i,1),e.className=n.join(" "))}function N(e){for(var t=e.childNodes,n=0,i=t.length;n<i;n++){var o=t[n];o.style&&o.removeAttribute("style");var r=o.attributes;if(r)for(var s=r.length-1;0<=s;s--){var a=r[s];!0===a.specified&&o.removeAttribute(a.name)}N(o)}}function k(e){var t,n;document.createRange&&((t=document.createRange()).selectNodeContents(e),t.collapse(!1),(n=window.getSelection()).removeAllRanges(),n.addRange(t))}function O(e){var t,n;e&&"DIV"===e.nodeName&&window.getSelection&&document.createRange&&((n=document.createRange()).selectNodeContents(e),(t=window.getSelection()).removeAllRanges(),t.addRange(n))}function I(){if(window.getSelection){var e=window.getSelection();if(e.getRangeAt&&e.rangeCount)return e.getRangeAt(0)}return null}function D(e){var t;e&&window.getSelection&&((t=window.getSelection()).removeAllRanges(),t.addRange(e))}function A(){var e=I();return e&&"startOffset"in e&&"endOffset"in e&&e.startContainer&&e.startContainer===e.endContainer?{startOffset:e.startOffset,endOffset:e.endOffset,container:e.startContainer.parentNode}:null}function P(e){var t;document.createRange&&window.getSelection&&window.getSelection()&&(t=document.createRange(),e.container.firstChild||e.container.appendChild(document.createTextNode("")),t.setStart(e.container.firstChild,e.startOffset),t.setEnd(e.container.firstChild,e.endOffset),D(t))}function F(e,t){if(void 0===t&&(t={_text:"",flush:function(){var e=this._text;return this._text="",e},set:function(e){this._text=e}}),e.nodeValue){var n=e.nodeValue.replace(/\s*\n\s*/g,"");return""!==n?t.flush()+n:""}if(e.hasChildNodes()){for(var i=e.childNodes,o="",r=0,s=i.length;r<s;r++){var a,l,c=i[r];"DIV"===c.nodeName||"P"===c.nodeName?((l=(a=i[r-1])?a.nodeName:void 0)&&"DIV"!==l&&"P"!==l&&"BR"!==l&&(""!==o&&(o+="\n"),t.flush()),o+=F(c,t),t.set("\n")):"BR"===c.nodeName?(o+=t.flush(),t.set("\n")):o+=F(c,t)}return o}return""}function M(e,t){for(var n=e?e.parentNode:void 0;n;){if(n===t)return!0;n=n.parentNode}return!1}function V(){var e,t;return-1===L&&(e=-1,"undefined"!=typeof navigator&&"Microsoft Internet Explorer"===navigator.appName&&(t=navigator.userAgent,null!=new RegExp("MSIE ([0-9]+[.0-9]+)").exec(t)&&(e=parseFloat(RegExp.$1))),L=e),L}function B(){return"undefined"!=typeof navigator&&-1!==navigator.userAgent.indexOf("Firefox")}var L=-1;function R(e,t,n,i){if(e.addEventListener)return void 0===i&&(i=!1),"mousewheel"===t&&B()&&(t="DOMMouseScroll"),e.addEventListener(t,n,i),n;if(e.attachEvent){var o=function(){return n.call(e,window.event)};return e.attachEvent("on"+t,o),o}}function z(e,t,n,i){e.removeEventListener?(void 0===i&&(i=!1),"mousewheel"===t&&B()&&(t="DOMMouseScroll"),e.removeEventListener(t,n,i)):e.detachEvent&&e.detachEvent("on"+t,n)}function H(e,t){for(var n=e.parentNode;n;){if(n===t)return!0;n=n.parentNode}return!1}function U(n){var e=[],i=0;function t(e){for(var t="";void 0!==n[i]&&n[i]!==e;)t+=n[i],i++;if(n[i]!==e)throw new Error("Invalid JSON path: unexpected end, character "+e+" expected");return t}for(;void 0!==n[i];)if("."===n[i])i++,e.push(function(){for(var e="";void 0!==n[i]&&/[\w$]/.test(n[i]);)e+=n[i],i++;if(""===e)throw new Error("Invalid JSON path: property name expected at index "+i);return e}());else{if("["!==n[i])throw new Error('Invalid JSON path: unexpected character "'+n[i]+'" at index '+i);if("'"===n[++i]||'"'===n[i]){var o=n[i];if(i++,e.push(t(o)),n[i]!==o)throw new Error("Invalid JSON path: closing quote ' expected at index "+i);i++}else{var r=t("]").trim();if(0===r.length)throw new Error("Invalid JSON path: array value expected at index "+i);r="*"===r?r:JSON.parse(r),e.push(r)}if("]"!==n[i])throw new Error("Invalid JSON path: closing bracket ] expected at index "+i);i++}return e}function J(e){return e.map(function(e){return"number"==typeof e?"["+e+"]":"string"==typeof e&&e.match(/^[A-Za-z0-9_$]+$/)?"."+e:'["'+e+'"]'}).join("")}function $(e){var t,n;return"enum"!==e.keyword||!Array.isArray(e.schema)||(n=e.schema)&&(5<(n=n.map(function(e){return JSON.stringify(e)})).length&&(t=["("+(n.length-5)+" more...)"],(n=n.slice(0,5)).push(t)),e.message="should be equal to one of: "+n.join(", ")),"additionalProperties"===e.keyword&&(e.message="should NOT have additional property: "+e.params.additionalProperty),e}function q(e){return e&&"function"==typeof e.then&&"function"==typeof e.catch}function K(e){return"object"===d(e)&&Array.isArray(e.path)&&"string"==typeof e.message}function Q(e,t,n){var i=void 0!==n?n:0;return t.left-i>=e.left&&t.right+i<=e.right&&t.top-i>=e.top&&t.bottom+i<=e.bottom}function W(i,o,r){var s;return function(){var e=this,t=arguments,n=r&&!s;clearTimeout(s),s=setTimeout(function(){s=null,r||i.apply(e,t)},o),n&&i.apply(e,t)}}function X(e,t){for(var n=t.length,i=0,o=e.length,r=t.length;t.charAt(i)===e.charAt(i)&&i<n;)i++;for(;t.charAt(r-1)===e.charAt(o-1)&&i<r&&0<o;)r--,o--;return{start:i,end:r}}function Z(n){var e,t,i,o,r,s=0,a=0;return"number"==typeof n.selectionStart&&"number"==typeof n.selectionEnd?(s=n.selectionStart,a=n.selectionEnd):(t=document.selection.createRange())&&t.parentElement()===n&&(o=n.value.length,e=n.value.replace(/\r\n/g,"\n"),(i=n.createTextRange()).moveToBookmark(t.getBookmark()),(r=n.createTextRange()).collapse(!1),-1<i.compareEndPoints("StartToEnd",r)?s=a=o:(s=-i.moveStart("character",-o),s+=e.slice(0,s).split("\n").length-1,-1<i.compareEndPoints("EndToEnd",r)?a=o:(a=-i.moveEnd("character",-o),a+=e.slice(0,a).split("\n").length-1))),{startIndex:s,endIndex:a,start:l(s),end:l(a)};function l(e){var t=n.value.substring(0,e);return{row:(t.match(/\n/g)||[]).length+1,column:t.length-t.lastIndexOf("\n")}}}function Y(e,t,n){var i=e.value||"";if(0<t&&0<n){var o=i.split("\n",t);t=Math.min(o.length,t),n=Math.min(o[t-1].length,n-1);var r=1===t?n:n+1;return o.slice(0,t-1).join("\n").length+r}return-1}function G(e,t){var i,o=[];if(!t||!t.length)return o;try{i=l.a.parse(e)}catch(e){return o}return t.forEach(function(e){var t=ee(U(e)),n=i.pointers[t];n&&o.push({path:e,line:n.key?n.key.line:n.value?n.value.line:0,column:n.key?n.key.column:n.value?n.value.column:0})}),o}function ee(e){return e.map(function(e){return"/"+String(e).replace(/~/g,"~0").replace(/\//g,"~1")}).join("")}function te(e){var t=document.createElement("div");return t.style.color=e,t.style.color.split(/\s+/).join("").toLowerCase()||null}function ne(e){return!!te(e)}function ie(n,e){if(!n)return"";var i="";return n.title&&(i+=n.title),n.description&&(0<i.length&&(i+="\n"),i+=n.description),n.default&&(0<i.length&&(i+="\n\n"),i+=Object(c.c)("default",void 0,e)+"\n",i+=JSON.stringify(n.default,null,2)),Array.isArray(n.examples)&&0<n.examples.length&&(0<i.length&&(i+="\n\n"),i+=Object(c.c)("examples",void 0,e)+"\n",n.examples.forEach(function(e,t){i+=JSON.stringify(e,null,2),t!==n.examples.length-1&&(i+="\n")})),i}function oe(e,t){for(var n=e,i=0;i<t.length&&null!=n;i++)n=n[t[i]];return n}function re(e,t){for(var n=e.replace(/ \(copy( \d+)?\)$/,""),i=n,o=1;-1!==t.indexOf(i);){i=n+" ("+("copy"+(1<o?" "+o:""))+")";o++}return i}function se(e,t){var n={};if(Array.isArray(e))for(var i=Math.min(e.length,h),o=0;o<i;o++){!function t(n,i,o,r){(Array.isArray(n)||pe(n))&&!r||(i[o||""]=!0),pe(n)&&Object.keys(n).forEach(function(e){t(n[e],i,o+"."+e,r)})}(e[o],n,"",t)}else n[""]=!0;return Object.keys(n).sort()}function ae(e,t,n){var o=t&&"."!==t?U(t):[],r="desc"===n?-1:1,i=e.slice();return i.sort(function(e,t){var n=oe(e,o),i=oe(t,o);return r*(i<n?1:n<i?-1:0)}),i}function le(t,e){var n="desc"===e?-1:1,i=Object.keys(t).sort(function(e,t){return n*r()(e,t)}),o={};return i.forEach(function(e){o[e]=t[e]}),o}function ce(e){if(""===e)return"";var t=e.toLowerCase();if("null"===t)return null;if("true"===t)return!0;if("false"===t)return!1;var n=Number(e),i=parseFloat(e);return isNaN(n)||isNaN(i)?e:n}function de(e,t){return"number"==typeof t&&u<t&&isFinite(t)&&Math.floor(t)===t&&!isNaN(new Date(t).valueOf())}function he(e){if(e<900)return e.toFixed()+" B";var t=e/1e3;if(t<900)return t.toFixed(1)+" KB";var n=t/1e3;if(n<900)return n.toFixed(1)+" MB";var i=n/1e3;return i<900?i.toFixed(1)+" GB":(i/1e3).toFixed(1)+" TB"}function ue(e,t){return e.length<=t?e:e.slice(0,t)+"..."}function pe(e){return"object"===d(e)&&null!==e&&!Array.isArray(e)}function fe(e,t){return-1!==e.indexOf(t)}function me(n,e){if(!e&&!n)return!1;if(e&&!n||!e&&n)return!0;if(e.length!==n.length)return!0;for(var t=0;t<n.length;++t){var i=function(t){if(!("error"===n[t].type?e.find(function(e){return e.line===n[t].line}):e.find(function(e){return e.dataPath===n[t].dataPath&&e.schemaPath===n[t].schemaPath})))return{v:!0}}(t);if("object"===d(i))return i.v}return!1}function ve(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},function(e,t,n){"use strict";n.d(t,"a",function(){return l}),n.d(t,"b",function(){return c}),n.d(t,"c",function(){return d});n(18);var i=["en","pt-BR","zh-CN","tr","ja","fr-FR"],r={en:{array:"Array",auto:"Auto",appendText:"Append",appendTitle:"Append a new field with type 'auto' after this field (Ctrl+Shift+Ins)",appendSubmenuTitle:"Select the type of the field to be appended",appendTitleAuto:"Append a new field with type 'auto' (Ctrl+Shift+Ins)",ascending:"Ascending",ascendingTitle:"Sort the childs of this ${type} in ascending order",actionsMenu:"Click to open the actions menu (Ctrl+M)",cannotParseFieldError:"Cannot parse field into JSON",cannotParseValueError:"Cannot parse value into JSON",collapseAll:"Collapse all fields",compactTitle:"Compact JSON data, remove all whitespaces (Ctrl+Shift+\\)",descending:"Descending",descendingTitle:"Sort the childs of this ${type} in descending order",drag:"Drag to move this field (Alt+Shift+Arrows)",duplicateKey:"duplicate key",duplicateText:"Duplicate",duplicateTitle:"Duplicate selected fields (Ctrl+D)",duplicateField:"Duplicate this field (Ctrl+D)",duplicateFieldError:"Duplicate field name",empty:"empty",expandAll:"Expand all fields",expandTitle:"Click to expand/collapse this field (Ctrl+E). \nCtrl+Click to expand/collapse including all childs.",formatTitle:"Format JSON data, with proper indentation and line feeds (Ctrl+\\)",insert:"Insert",insertTitle:"Insert a new field with type 'auto' before this field (Ctrl+Ins)",insertSub:"Select the type of the field to be inserted",object:"Object",ok:"Ok",redo:"Redo (Ctrl+Shift+Z)",removeText:"Remove",removeTitle:"Remove selected fields (Ctrl+Del)",removeField:"Remove this field (Ctrl+Del)",repairTitle:"Repair JSON: fix quotes and escape characters, remove comments and JSONP notation, turn JavaScript objects into JSON.",searchTitle:"Search fields and values",searchNextResultTitle:"Next result (Enter)",searchPreviousResultTitle:"Previous result (Shift + Enter)",selectNode:"Select a node...",showAll:"show all",showMore:"show more",showMoreStatus:"displaying ${visibleChilds} of ${totalChilds} items.",sort:"Sort",sortTitle:"Sort the childs of this ${type}",sortTitleShort:"Sort contents",sortFieldLabel:"Field:",sortDirectionLabel:"Direction:",sortFieldTitle:"Select the nested field by which to sort the array or object",sortAscending:"Ascending",sortAscendingTitle:"Sort the selected field in ascending order",sortDescending:"Descending",sortDescendingTitle:"Sort the selected field in descending order",string:"String",transform:"Transform",transformTitle:"Filter, sort, or transform the childs of this ${type}",transformTitleShort:"Filter, sort, or transform contents",extract:"Extract",extractTitle:"Extract this ${type}",transformQueryTitle:"Enter a JMESPath query",transformWizardLabel:"Wizard",transformWizardFilter:"Filter",transformWizardSortBy:"Sort by",transformWizardSelectFields:"Select fields",transformQueryLabel:"Query",transformPreviewLabel:"Preview",type:"Type",typeTitle:"Change the type of this field",openUrl:"Ctrl+Click or Ctrl+Enter to open url in new window",undo:"Undo last action (Ctrl+Z)",validationCannotMove:"Cannot move a field into a child of itself",autoType:'Field type "auto". The field type is automatically determined from the value and can be a string, number, boolean, or null.',objectType:'Field type "object". An object contains an unordered set of key/value pairs.',arrayType:'Field type "array". An array contains an ordered collection of values.',stringType:'Field type "string". Field type is not determined from the value, but always returned as string.',modeEditorTitle:"Switch Editor Mode",modeCodeText:"Code",modeCodeTitle:"Switch to code highlighter",modeFormText:"Form",modeFormTitle:"Switch to form editor",modeTextText:"Text",modeTextTitle:"Switch to plain text editor",modeTreeText:"Tree",modeTreeTitle:"Switch to tree editor",modeViewText:"View",modeViewTitle:"Switch to tree view",modePreviewText:"Preview",modePreviewTitle:"Switch to preview mode",examples:"Examples",default:"Default"},"zh-CN":{array:"数组",auto:"自动",appendText:"追加",appendTitle:"在此字段后追加一个类型为“auto”的新字段 (Ctrl+Shift+Ins)",appendSubmenuTitle:"选择要追加的字段类型",appendTitleAuto:"追加类型为“auto”的新字段 (Ctrl+Shift+Ins)",ascending:"升序",ascendingTitle:"升序排列${type}的子节点",actionsMenu:"点击打开动作菜单(Ctrl+M)",cannotParseFieldError:"无法将字段解析为JSON",cannotParseValueError:"无法将值解析为JSON",collapseAll:"缩进所有字段",compactTitle:"压缩JSON数据，删除所有空格 (Ctrl+Shift+\\)",descending:"降序",descendingTitle:"降序排列${type}的子节点",drag:"拖拽移动该节点(Alt+Shift+Arrows)",duplicateKey:"重复键",duplicateText:"复制",duplicateTitle:"复制选中字段(Ctrl+D)",duplicateField:"复制该字段(Ctrl+D)",duplicateFieldError:"重复的字段名称",empty:"清空",expandAll:"展开所有字段",expandTitle:"点击 展开/收缩 该字段(Ctrl+E). \nCtrl+Click 展开/收缩 包含所有子节点.",formatTitle:"使用适当的缩进和换行符格式化JSON数据 (Ctrl+\\)",insert:"插入",insertTitle:"在此字段前插入类型为“auto”的新字段 (Ctrl+Ins)",insertSub:"选择要插入的字段类型",object:"对象",ok:"Ok",redo:"重做 (Ctrl+Shift+Z)",removeText:"移除",removeTitle:"移除选中字段 (Ctrl+Del)",removeField:"移除该字段 (Ctrl+Del)",repairTitle:"修复JSON：修复引号和转义符，删除注释和JSONP表示法，将JavaScript对象转换为JSON。",selectNode:"选择一个节点...",showAll:"展示全部",showMore:"展示更多",showMoreStatus:"显示${totalChilds}的${visibleChilds}项目.",sort:"排序",sortTitle:"排序${type}的子节点",sortTitleShort:"内容排序",sortFieldLabel:"字段：",sortDirectionLabel:"方向：",sortFieldTitle:"选择用于对数组或对象排序的嵌套字段",sortAscending:"升序排序",sortAscendingTitle:"按照该字段升序排序",sortDescending:"降序排序",sortDescendingTitle:"按照该字段降序排序",string:"字符串",transform:"变换",transformTitle:"筛选，排序，或者转换${type}的子节点",transformTitleShort:"筛选，排序，或者转换内容",extract:"提取",extractTitle:"提取这个 ${type}",transformQueryTitle:"输入JMESPath查询",transformWizardLabel:"向导",transformWizardFilter:"筛选",transformWizardSortBy:"排序",transformWizardSelectFields:"选择字段",transformQueryLabel:"查询",transformPreviewLabel:"预览",type:"类型",typeTitle:"更改字段类型",openUrl:"Ctrl+Click 或者 Ctrl+Enter 在新窗口打开链接",undo:"撤销上次动作 (Ctrl+Z)",validationCannotMove:"无法将字段移入其子节点",autoType:'字段类型 "auto". 字段类型由值自动确定 可以为 string，number，boolean，或者 null.',objectType:'字段类型 "object". 对象包含一组无序的键/值对.',arrayType:'字段类型 "array". 数组包含值的有序集合.',stringType:'字段类型 "string". 字段类型由值自动确定，但始终作为字符串返回.',modeCodeText:"代码",modeCodeTitle:"切换至代码高亮",modeFormText:"表单",modeFormTitle:"切换至表单编辑",modeTextText:"文本",modeTextTitle:"切换至文本编辑",modeTreeText:"树",modeTreeTitle:"切换至树编辑",modeViewText:"视图",modeViewTitle:"切换至树视图",modePreviewText:"预览",modePreviewTitle:"切换至预览模式",examples:"例子",default:"缺省"},"pt-BR":{array:"Lista",auto:"Automatico",appendText:"Adicionar",appendTitle:"Adicionar novo campo com tipo 'auto' depois deste campo (Ctrl+Shift+Ins)",appendSubmenuTitle:"Selecione o tipo do campo a ser adicionado",appendTitleAuto:"Adicionar novo campo com tipo 'auto' (Ctrl+Shift+Ins)",ascending:"Ascendente",ascendingTitle:"Organizar filhor do tipo ${type} em crescente",actionsMenu:"Clique para abrir o menu de ações (Ctrl+M)",cannotParseFieldError:"Não é possível analisar o campo no JSON",cannotParseValueError:"Não é possível analisar o valor em JSON",collapseAll:"Fechar todos campos",compactTitle:"Dados JSON compactos, remova todos os espaços em branco (Ctrl+Shift+\\)",descending:"Descendente",descendingTitle:"Organizar o filhos do tipo ${type} em decrescente",duplicateKey:"chave duplicada",drag:"Arraste para mover este campo (Alt+Shift+Arrows)",duplicateText:"Duplicar",duplicateTitle:"Duplicar campos selecionados (Ctrl+D)",duplicateField:"Duplicar este campo (Ctrl+D)",duplicateFieldError:"Nome do campo duplicado",empty:"vazio",expandAll:"Expandir todos campos",expandTitle:"Clique para expandir/encolher este campo (Ctrl+E). \nCtrl+Click para expandir/encolher incluindo todos os filhos.",formatTitle:"Formate dados JSON, com recuo e feeds de linha adequados (Ctrl+\\)",insert:"Inserir",insertTitle:"Inserir um novo campo do tipo 'auto' antes deste campo (Ctrl+Ins)",insertSub:"Selecionar o tipo de campo a ser inserido",object:"Objeto",ok:"Ok",redo:"Refazer (Ctrl+Shift+Z)",removeText:"Remover",removeTitle:"Remover campos selecionados (Ctrl+Del)",removeField:"Remover este campo (Ctrl+Del)",repairTitle:"Repare JSON: corrija aspas e caracteres de escape, remova comentários e notação JSONP, transforme objetos JavaScript em JSON.",selectNode:"Selecione um nódulo...",showAll:"mostrar todos",showMore:"mostrar mais",showMoreStatus:"exibindo ${visibleChilds} de ${totalChilds} itens.",sort:"Organizar",sortTitle:"Organizar os filhos deste ${type}",sortTitleShort:"Organizar os filhos",sortFieldLabel:"Campo:",sortDirectionLabel:"Direção:",sortFieldTitle:"Selecione um campo filho pelo qual ordenar o array ou objeto",sortAscending:"Ascendente",sortAscendingTitle:"Ordenar o campo selecionado por ordem ascendente",sortDescending:"Descendente",sortDescendingTitle:"Ordenar o campo selecionado por ordem descendente",string:"Texto",transform:"Transformar",transformTitle:"Filtrar, ordenar ou transformar os filhos deste ${type}",transformTitleShort:"Filtrar, ordenar ou transformar conteúdos",transformQueryTitle:"Insira uma expressão JMESPath",transformWizardLabel:"Assistente",transformWizardFilter:"Filtro",transformWizardSortBy:"Ordenar por",transformWizardSelectFields:"Selecionar campos",transformQueryLabel:"Expressão",transformPreviewLabel:"Visualizar",type:"Tipo",typeTitle:"Mudar o tipo deste campo",openUrl:"Ctrl+Click ou Ctrl+Enter para abrir link em nova janela",undo:"Desfazer último ação (Ctrl+Z)",validationCannotMove:"Não pode mover um campo como filho dele mesmo",autoType:'Campo do tipo "auto". O tipo do campo é determinao automaticamente a partir do seu valor e pode ser texto, número, verdade/falso ou nulo.',objectType:'Campo do tipo "objeto". Um objeto contém uma lista de pares com chave e valor.',arrayType:'Campo do tipo "lista". Uma lista contem uma coleção de valores ordenados.',stringType:'Campo do tipo "string". Campo do tipo nao é determinado através do seu valor, mas sempre retornara um texto.',examples:"Exemplos",default:"Revelia"},tr:{array:"Dizin",auto:"Otomatik",appendText:"Ekle",appendTitle:"Bu alanın altına 'otomatik' tipinde yeni bir alan ekle (Ctrl+Shift+Ins)",appendSubmenuTitle:"Eklenecek alanın tipini seç",appendTitleAuto:"'Otomatik' tipinde yeni bir alan ekle (Ctrl+Shift+Ins)",ascending:"Artan",ascendingTitle:"${type}'ın alt tiplerini artan düzende sırala",actionsMenu:"Aksiyon menüsünü açmak için tıklayın (Ctrl+M)",collapseAll:"Tüm alanları kapat",descending:"Azalan",descendingTitle:"${type}'ın alt tiplerini azalan düzende sırala",drag:"Bu alanı taşımak için sürükleyin (Alt+Shift+Arrows)",duplicateKey:"Var olan anahtar",duplicateText:"Aşağıya kopyala",duplicateTitle:"Seçili alanlardan bir daha oluştur (Ctrl+D)",duplicateField:"Bu alandan bir daha oluştur (Ctrl+D)",duplicateFieldError:"Duplicate field name",cannotParseFieldError:"Alan JSON'a ayrıştırılamıyor",cannotParseValueError:"JSON'a değer ayrıştırılamıyor",empty:"boş",expandAll:"Tüm alanları aç",expandTitle:"Bu alanı açmak/kapatmak için tıkla (Ctrl+E). \nAlt alanlarda dahil tüm alanları açmak için Ctrl+Click ",insert:"Ekle",insertTitle:"Bu alanın üstüne 'otomatik' tipinde yeni bir alan ekle (Ctrl+Ins)",insertSub:"Araya eklenecek alanın tipini seç",object:"Nesne",ok:"Tamam",redo:"Yeniden yap (Ctrl+Shift+Z)",removeText:"Kaldır",removeTitle:"Seçilen alanları kaldır (Ctrl+Del)",removeField:"Bu alanı kaldır (Ctrl+Del)",selectNode:"Bir nesne seç...",showAll:"tümünü göster",showMore:"daha fazla göster",showMoreStatus:"${totalChilds} alanın ${visibleChilds} alt alanları gösteriliyor",sort:"Sırala",sortTitle:"${type}'ın alt alanlarını sırala",sortTitleShort:"İçerikleri sırala",sortFieldLabel:"Alan:",sortDirectionLabel:"Yön:",sortFieldTitle:"Diziyi veya nesneyi sıralamak için iç içe geçmiş alanı seçin",sortAscending:"Artan",sortAscendingTitle:"Seçili alanı artan düzende sırala",sortDescending:"Azalan",sortDescendingTitle:"Seçili alanı azalan düzende sırala",string:"Karakter Dizisi",transform:"Dönüştür",transformTitle:"${type}'ın alt alanlarını filtrele, sırala veya dönüştür",transformTitleShort:"İçerikleri filterele, sırala veya dönüştür",transformQueryTitle:"JMESPath sorgusu gir",transformWizardLabel:"Sihirbaz",transformWizardFilter:"Filtre",transformWizardSortBy:"Sırala",transformWizardSelectFields:"Alanları seç",transformQueryLabel:"Sorgu",transformPreviewLabel:"Önizleme",type:"Tip",typeTitle:"Bu alanın tipini değiştir",openUrl:"URL'i yeni bir pencerede açmak için Ctrl+Click veya Ctrl+Enter",undo:"Son değişikliği geri al (Ctrl+Z)",validationCannotMove:"Alt alan olarak taşınamıyor",autoType:'Alan tipi "otomatik". Alan türü otomatik olarak değerden belirlenirve bir dize, sayı, boolean veya null olabilir.',objectType:'Alan tipi "nesne". Bir nesne, sıralanmamış bir anahtar / değer çifti kümesi içerir.',arrayType:'Alan tipi "dizi". Bir dizi, düzenli değerler koleksiyonu içerir.',stringType:'Alan tipi "karakter dizisi". Alan türü değerden belirlenmez,ancak her zaman karakter dizisi olarak döndürülür.',modeCodeText:"Kod",modeCodeTitle:"Kod vurgulayıcıya geç",modeFormText:"Form",modeFormTitle:"Form düzenleyiciye geç",modeTextText:"Metin",modeTextTitle:"Düz metin düzenleyiciye geç",modeTreeText:"Ağaç",modeTreeTitle:"Ağaç düzenleyiciye geç",modeViewText:"Görünüm",modeViewTitle:"Ağaç görünümüne geç",examples:"Örnekler",default:"Varsayılan"},ja:{array:"配列",auto:"オート",appendText:"追加",appendTitle:'次のフィールドに"オート"のフィールドを追加 (Ctrl+Shift+Ins)',appendSubmenuTitle:"追加するフィールドの型を選択してください",appendTitleAuto:'"オート"のフィールドを追加 (Ctrl+Shift+Ins)',ascending:"昇順",ascendingTitle:"${type}の子要素を昇順に並べ替え",actionsMenu:"クリックしてアクションメニューを開く (Ctrl+M)",collapseAll:"すべてを折りたたむ",descending:"降順",descendingTitle:"${type}の子要素を降順に並べ替え",drag:"ドラッグして選択中のフィールドを移動 (Alt+Shift+Arrows)",duplicateKey:"複製キー",duplicateText:"複製",duplicateTitle:"選択中のフィールドを複製 (Ctrl+D)",duplicateField:"選択中のフィールドを複製 (Ctrl+D)",duplicateFieldError:"フィールド名が重複しています",cannotParseFieldError:"JSONのフィールドを解析できません",cannotParseValueError:"JSONの値を解析できません",empty:"空",expandAll:"すべてを展開",expandTitle:"クリックしてフィールドを展開/折りたたむ (Ctrl+E). \nCtrl+Click ですべての子要素を展開/折りたたむ",insert:"挿入",insertTitle:"選択中のフィールドの前に新しいフィールドを挿入 (Ctrl+Ins)",insertSub:"挿入するフィールドの型を選択",object:"オブジェクト",ok:"実行",redo:"やり直す (Ctrl+Shift+Z)",removeText:"削除",removeTitle:"選択中のフィールドを削除 (Ctrl+Del)",removeField:"選択中のフィールドを削除 (Ctrl+Del)",selectNode:"ノードを選択...",showAll:"すべてを表示",showMore:"もっと見る",showMoreStatus:"${totalChilds}個のアイテムのうち ${visibleChilds}個を表示しています。",sort:"並べ替え",sortTitle:"${type}の子要素を並べ替え",sortTitleShort:"並べ替え",sortFieldLabel:"フィールド:",sortDirectionLabel:"順序:",sortFieldTitle:"配列またはオブジェクトを並び替えるためのフィールドを選択",sortAscending:"昇順",sortAscendingTitle:"選択中のフィールドを昇順に並び替え",sortDescending:"降順",sortDescendingTitle:"選択中のフィールドを降順に並び替え",string:"文字列",transform:"変換",transformTitle:"${type}の子要素をフィルター・並び替え・変換する",transformTitleShort:"内容をフィルター・並び替え・変換する",extract:"抽出",extractTitle:"${type}を抽出",transformQueryTitle:"JMESPathクエリを入力",transformWizardLabel:"ウィザード",transformWizardFilter:"フィルター",transformWizardSortBy:"並び替え",transformWizardSelectFields:"フィールドを選択",transformQueryLabel:"クエリ",transformPreviewLabel:"プレビュー",type:"型",typeTitle:"選択中のフィールドの型を変更",openUrl:"Ctrl+Click または Ctrl+Enter で 新規ウィンドウでURLを開く",undo:"元に戻す (Ctrl+Z)",validationCannotMove:"子要素に移動できません ",autoType:"オート： フィールドの型は値から自動的に決定されます。 (文字列・数値・ブール・null)",objectType:"オブジェクト： オブジェクトは順序が決まっていないキーと値のペア組み合わせです。",arrayType:"配列： 配列は順序が決まっている値の集合体です。",stringType:"文字列： フィールド型は値から決定されませんが、常に文字列として返されます。",modeCodeText:"コードモード",modeCodeTitle:"ハイライトモードに切り替え",modeFormText:"フォームモード",modeFormTitle:"フォームモードに切り替え",modeTextText:"テキストモード",modeTextTitle:"テキストモードに切り替え",modeTreeText:"ツリーモード",modeTreeTitle:"ツリーモードに切り替え",modeViewText:"ビューモード",modeViewTitle:"ビューモードに切り替え",modePreviewText:"プレビュー",modePreviewTitle:"プレビューに切り替え",examples:"例",default:"デフォルト"},"fr-FR":{array:"Liste",auto:"Auto",appendText:"Ajouter",appendTitle:"Ajouter un champ de type 'auto' après ce champ (Ctrl+Shift+Ins)",appendSubmenuTitle:"Sélectionner le type du champ à ajouter",appendTitleAuto:"Ajouter un champ de type 'auto' (Ctrl+Shift+Ins)",ascending:"Ascendant",ascendingTitle:"Trier les enfants de ce ${type} par ordre ascendant",actionsMenu:"Ouvrir le menu des actions (Ctrl+M)",collapseAll:"Regrouper",descending:"Descendant",descendingTitle:"Trier les enfants de ce ${type} par ordre descendant",drag:"Déplacer (Alt+Shift+Arrows)",duplicateKey:"Dupliquer la clé",duplicateText:"Dupliquer",duplicateTitle:"Dupliquer les champs sélectionnés (Ctrl+D)",duplicateField:"Dupliquer ce champ (Ctrl+D)",duplicateFieldError:"Dupliquer le nom de champ",cannotParseFieldError:"Champ impossible à parser en JSON",cannotParseValueError:"Valeur impossible à parser en JSON",empty:"vide",expandAll:"Étendre",expandTitle:"Étendre/regrouper ce champ (Ctrl+E). \nCtrl+Click pour étendre/regrouper avec tous les champs.",insert:"Insérer",insertTitle:"Insérer un champ de type 'auto' avant ce champ (Ctrl+Ins)",insertSub:"Sélectionner le type de champ à insérer",object:"Objet",ok:"Ok",redo:"Rejouer (Ctrl+Shift+Z)",removeText:"Supprimer",removeTitle:"Supprimer les champs sélectionnés (Ctrl+Del)",removeField:"Supprimer ce champ (Ctrl+Del)",searchTitle:"Rechercher champs et valeurs",searchNextResultTitle:"Résultat suivant (Enter)",searchPreviousResultTitle:"Résultat précédent (Shift + Enter)",selectNode:"Sélectionner un nœud...",showAll:"voir tout",showMore:"voir plus",showMoreStatus:"${visibleChilds} éléments affichés de ${totalChilds}.",sort:"Trier",sortTitle:"Trier les champs de ce ${type}",sortTitleShort:"Trier",sortFieldLabel:"Champ:",sortDirectionLabel:"Direction:",sortFieldTitle:"Sélectionner les champs permettant de trier les listes et objet",sortAscending:"Ascendant",sortAscendingTitle:"Trier les champs sélectionnés par ordre ascendant",sortDescending:"Descendant",sortDescendingTitle:"Trier les champs sélectionnés par ordre descendant",string:"Chaîne",transform:"Transformer",transformTitle:"Filtrer, trier, or transformer les enfants de ce ${type}",transformTitleShort:"Filtrer, trier ou transformer le contenu",extract:"Extraire",extractTitle:"Extraire ce ${type}",transformQueryTitle:"Saisir une requête JMESPath",transformWizardLabel:"Assistant",transformWizardFilter:"Filtrer",transformWizardSortBy:"Trier par",transformWizardSelectFields:"Sélectionner les champs",transformQueryLabel:"Requête",transformPreviewLabel:"Prévisualisation",type:"Type",typeTitle:"Changer le type de ce champ",openUrl:"Ctrl+Click ou Ctrl+Enter pour ouvrir l'url dans une autre fenêtre",undo:"Annuler la dernière action (Ctrl+Z)",validationCannotMove:"Cannot move a field into a child of itself",autoType:'Champe de type "auto". Ce type de champ est automatiquement déterminé en fonction de la valeur et peut être de type "chaîne", "nombre", "booléen" ou null.',objectType:'Champ de type "objet". Un objet contient un ensemble non ordonné de paires clé/valeur.',arrayType:'Champ de type "liste". Une liste contient une collection ordonnée de valeurs.',stringType:'Champ de type "chaîne". Ce type de champ n\'est pas déterminé en fonction de la valeur, mais retourne systématiquement une chaîne de caractères.',modeEditorTitle:"Changer mode d'édition",modeCodeText:"Code",modeCodeTitle:"Activer surlignage code",modeFormText:"Formulaire",modeFormTitle:"Activer formulaire",modeTextText:"Texte",modeTextTitle:"Activer éditeur texte",modeTreeText:"Arbre",modeTreeTitle:"Activer éditeur arbre",modeViewText:"Lecture seule",modeViewTitle:"Activer vue arbre",modePreviewText:"Prévisualisation",modePreviewTitle:"Activer mode prévisualiser",examples:"Exemples",default:"Défaut"}},s="en",o="undefined"!=typeof navigator?navigator.language||navigator.userLanguage:void 0,a=i.find(function(e){return e===o})||s;function l(t){var e;t&&((e=i.find(function(e){return e===t}))?a=e:console.error("Language not found"))}function c(e){if(e){for(var t in e)!function(t){i.find(function(e){return e===t})||i.push(t),r[t]=Object.assign({},r[s],r[t],e[t])}(t)}}function d(e,t,n){var i=r[n=n||a][e]||r[s][e]||e;if(t)for(var o in t)i=i.replace("${"+o+"}",t[o]);return i}},function(e,t,n){"use strict";n.d(t,"a",function(){return i}),n.d(t,"d",function(){return o}),n.d(t,"b",function(){return r}),n.d(t,"c",function(){return s});var i=document.body,o=10485760,r=2e4,s=2147483648},function(e,t,n){"use strict";n.d(t,"a",function(){return o});var u=n(11),p=n(0),x=n(1);function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var o=function(){function h(n,e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h),this.dom={};var b=this,t=this.dom;this.anchor=void 0,this.items=n,this.eventListeners={},this.selection=void 0,this.onClose=e?e.close:void 0;var i=document.createElement("div");i.className="jsoneditor-contextmenu-root",t.root=i;var o=document.createElement("div");o.className="jsoneditor-contextmenu",t.menu=o,i.appendChild(o);var r=document.createElement("ul");r.className="jsoneditor-menu",o.appendChild(r),t.list=r,t.items=[];var s=document.createElement("button");s.type="button",t.focusButton=s;var a=document.createElement("li");a.style.overflow="hidden",a.style.height="0",a.appendChild(s),r.appendChild(a),function v(g,y,e){e.forEach(function(t){var e,n,i,o,r,s,a,l,c,d,h,u,p,f,m;"separator"===t.type?((e=document.createElement("div")).className="jsoneditor-separator",(n=document.createElement("li")).appendChild(e),g.appendChild(n)):(i={},o=document.createElement("li"),g.appendChild(o),(r=document.createElement("button")).type="button",r.className=t.className,i.button=r,t.title&&(r.title=t.title),t.click&&(r.onclick=function(e){e.preventDefault(),b.hide(),t.click()}),o.appendChild(r),t.submenu?((s=document.createElement("div")).className="jsoneditor-icon",r.appendChild(s),(a=document.createElement("div")).className="jsoneditor-text"+(t.click?"":" jsoneditor-right-margin"),a.appendChild(document.createTextNode(t.text)),r.appendChild(a),(l=t.click?(r.className+=" jsoneditor-default",(c=document.createElement("button")).type="button",(i.buttonExpand=c).className="jsoneditor-expand",(d=document.createElement("div")).className="jsoneditor-expand",c.appendChild(d),o.appendChild(c),t.submenuTitle&&(c.title=t.submenuTitle),c):((h=document.createElement("div")).className="jsoneditor-expand",r.appendChild(h),r)).onclick=function(e){e.preventDefault(),b._onExpandItem(i),l.focus()},u=[],i.subItems=u,p=document.createElement("ul"),(i.ul=p).className="jsoneditor-menu",p.style.height="0",o.appendChild(p),v(p,u,t.submenu)):((f=document.createElement("div")).className="jsoneditor-icon",r.appendChild(f),(m=document.createElement("div")).className="jsoneditor-text",m.appendChild(document.createTextNode(Object(x.c)(t.text))),r.appendChild(m)),y.push(i))})}(r,this.dom.items,n),this.maxHeight=0,n.forEach(function(e){var t=24*(n.length+(e.submenu?e.submenu.length:0));b.maxHeight=Math.max(b.maxHeight,t)})}var e,t,n;return e=h,(t=[{key:"_getVisibleButtons",value:function(){var t=[],n=this;return this.dom.items.forEach(function(e){t.push(e.button),e.buttonExpand&&t.push(e.buttonExpand),e.subItems&&e===n.expandedItem&&e.subItems.forEach(function(e){t.push(e.button),e.buttonExpand&&t.push(e.buttonExpand)})}),t}},{key:"show",value:function(e,t,n){this.hide();var i=!0,o=e.parentNode,r=e.getBoundingClientRect(),s=o.getBoundingClientRect(),a=t.getBoundingClientRect(),l=this;this.dom.absoluteAnchor=Object(u.a)(e,t,function(){l.hide()}),r.bottom+this.maxHeight<a.bottom||r.top-this.maxHeight>a.top&&(i=!1);var c,d=n?0:r.top-s.top;i?(c=e.offsetHeight,this.dom.menu.style.left="0",this.dom.menu.style.top=d+c+"px",this.dom.menu.style.bottom=""):(this.dom.menu.style.left="0",this.dom.menu.style.top="",this.dom.menu.style.bottom="0px"),this.dom.absoluteAnchor.appendChild(this.dom.root),this.selection=Object(p.getSelection)(),this.anchor=e,setTimeout(function(){l.dom.focusButton.focus()},0),h.visibleMenu&&h.visibleMenu.hide(),h.visibleMenu=this}},{key:"hide",value:function(){this.dom.absoluteAnchor&&(this.dom.absoluteAnchor.destroy(),delete this.dom.absoluteAnchor),this.dom.root.parentNode&&(this.dom.root.parentNode.removeChild(this.dom.root),this.onClose&&this.onClose()),h.visibleMenu===this&&(h.visibleMenu=void 0)}},{key:"_onExpandItem",value:function(n){var i,o=this,e=n===this.expandedItem,t=this.expandedItem;t&&(t.ul.style.height="0",t.ul.style.padding="",setTimeout(function(){o.expandedItem!==t&&(t.ul.style.display="",Object(p.removeClassName)(t.ul.parentNode,"jsoneditor-selected"))},300),this.expandedItem=void 0),e||((i=n.ul).style.display="block",i.clientHeight,setTimeout(function(){if(o.expandedItem===n){for(var e=0,t=0;t<i.childNodes.length;t++)e+=i.childNodes[t].clientHeight;i.style.height=e+"px",i.style.padding="5px 10px"}},0),Object(p.addClassName)(i.parentNode,"jsoneditor-selected"),this.expandedItem=n)}},{key:"_onKeyDown",value:function(e){var t,n,i,o,r=e.target,s=e.which,a=!1;27===s?(this.selection&&Object(p.setSelection)(this.selection),this.anchor&&this.anchor.focus(),this.hide(),a=!0):9===s?e.shiftKey?0===(n=(t=this._getVisibleButtons()).indexOf(r))&&(t[t.length-1].focus(),a=!0):(n=(t=this._getVisibleButtons()).indexOf(r))===t.length-1&&(t[0].focus(),a=!0):37===s?("jsoneditor-expand"===r.className&&(n=(t=this._getVisibleButtons()).indexOf(r),(i=t[n-1])&&i.focus()),a=!0):38===s?(n=(t=this._getVisibleButtons()).indexOf(r),(i=t[n-1])&&"jsoneditor-expand"===i.className&&(i=t[n-2]),(i=i||t[t.length-1])&&i.focus(),a=!0):39===s?(n=(t=this._getVisibleButtons()).indexOf(r),(o=t[n+1])&&"jsoneditor-expand"===o.className&&o.focus(),a=!0):40===s&&(n=(t=this._getVisibleButtons()).indexOf(r),(o=t[n+1])&&"jsoneditor-expand"===o.className&&(o=t[n+2]),(o=o||t[0])&&(o.focus(),a=!0),a=!0),a&&(e.stopPropagation(),e.preventDefault())}}])&&i(e.prototype,t),n&&i(e,n),h}();o.visibleMenu=void 0},function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return s});var i=n(20),o=n.n(i),l=n(0);function r(e,t){var n,i,o=t.sort,r=t.filter,s=t.projection,a="";return r?(n="@"!==r.field?["0"].concat(Object(l.parsePath)("."+r.field)):["0"],i="string"==typeof Object(l.get)(e,n)?r.value:Object(l.parseString)(r.value),a+="[? "+r.field+" "+r.relation+" `"+JSON.stringify(i)+"`]"):a+=Array.isArray(e)?"[*]":"@",o&&("desc"===o.direction?a+=" | reverse(sort_by(@, &"+o.field+"))":a+=" | sort_by(@, &"+o.field+")"),s&&("]"!==a[a.length-1]&&(a+=" | [*]"),1===s.fields.length?a+="."+s.fields[0]:1<s.fields.length&&(a+=".{"+s.fields.map(function(e){var t=e.split(".");return t[t.length-1]+": "+e}).join(", ")+"}")),a}function s(e,t){return o.a.search(e,t)}},function(e,t,n){"use strict";n.r(t),n.d(t,"showSortModal",function(){return s});var i=n(13),o=n.n(i),r=n(1),d=n(0);function s(e,t,s,n){var a=Array.isArray(t)?Object(d.getChildPaths)(t):[""],l=n&&n.path&&Object(d.contains)(a,n.path)?n.path:a[0],c=n&&n.direction||"asc",i='<div class="pico-modal-contents"><div class="pico-modal-header">'+Object(r.c)("sort")+"</div><form><table><tbody><tr>  <td>"+Object(r.c)("sortFieldLabel")+' </td>  <td class="jsoneditor-modal-input">  <div class="jsoneditor-select-wrapper">    <select id="field" title="'+Object(r.c)("sortFieldTitle")+'">    </select>  </div>  </td></tr><tr>  <td>'+Object(r.c)("sortDirectionLabel")+' </td>  <td class="jsoneditor-modal-input">  <div id="direction" class="jsoneditor-button-group"><input type="button" value="'+Object(r.c)("sortAscending")+'" title="'+Object(r.c)("sortAscendingTitle")+'" data-value="asc" class="jsoneditor-button-first jsoneditor-button-asc"/><input type="button" value="'+Object(r.c)("sortDescending")+'" title="'+Object(r.c)("sortDescendingTitle")+'" data-value="desc" class="jsoneditor-button-last jsoneditor-button-desc"/>  </div>  </td></tr><tr><td colspan="2" class="jsoneditor-modal-input jsoneditor-modal-actions">  <input type="submit" id="ok" value="'+Object(r.c)("ok")+'" /></td></tr></tbody></table></form></div>';o()({parent:e,content:i,overlayClass:"jsoneditor-modal-overlay",overlayStyles:{backgroundColor:"rgb(1,1,1)",opacity:.3},modalClass:"jsoneditor-modal jsoneditor-modal-sort"}).afterCreate(function(t){var e=t.modalElem().querySelector("form"),n=t.modalElem().querySelector("#ok"),i=t.modalElem().querySelector("#field"),o=t.modalElem().querySelector("#direction");function r(e){o.value=e,o.className="jsoneditor-button-group jsoneditor-button-group-value-"+o.value}a.forEach(function(e){var t,n=document.createElement("option");n.text=""===(t=e)?"@":"."===t[0]?t.slice(1):t,n.value=e,i.appendChild(n)}),i.value=l||a[0],r(c||"asc"),o.onclick=function(e){r(e.target.getAttribute("data-value"))},n.onclick=function(e){e.preventDefault(),e.stopPropagation(),t.close(),s({path:i.value,direction:o.value})},e&&(e.onsubmit=n.onclick)}).afterClose(function(e){e.destroy()}).show()}},function(e,t,n){"use strict";n.r(t),n.d(t,"showTransformModal",function(){return c});var i=n(13),r=n.n(i),o=n(9),N=n.n(o),s=n(1);function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function k(e,t,n,i){return"boolean"==typeof e||e instanceof Boolean||null===e||"number"==typeof e||e instanceof Number||"string"==typeof e||e instanceof String||e instanceof Date?JSON.stringify(e):Array.isArray(e)?function(e,t,n,i){for(var o=t?n+t:void 0,r=t?"[\n":"[",s=0;s<e.length;s++){var a=e[s];if(t&&(r+=o),r+=void 0!==a&&"function"!=typeof a?k(a,t,o,i):"null",s<e.length-1&&(r+=t?",\n":","),r.length>i)return r+"..."}return r+=t?"\n"+n+"]":"]"}(e,t,n,i):e&&"object"===a(e)?function(e,t,n,i){var o=t?n+t:void 0,r=!0,s=t?"{\n":"{";if("function"==typeof e.toJSON)return k(e.toJSON(),t,n,i);for(var a in e)if(function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}(e,a)){var l=e[a];if(r?r=!1:s+=t?",\n":",",s+=t?o+'"'+a+'": ':'"'+a+'":',(s+=k(l,t,o,i)).length>i)return s+"..."}return s+=t?"\n"+n+"}":"}"}(e,t,n,i):void 0}function O(e,t){for(var n="";0<t--;)n+=e;return n}var I=n(0),D=n(2),l='Enter a <a href="http://jmespath.org" target="_blank">JMESPath</a> query to filter, sort, or transform the JSON data.<br/>To learn JMESPath, go to <a href="http://jmespath.org/tutorial.html" target="_blank">the interactive tutorial</a>.';function c(e){var t=e.container,E=e.json,n=e.queryDescription,i=void 0===n?l:n,w=e.createQuery,T=e.executeQuery,j=e.onTransform,S=E,o='<label class="pico-modal-contents"><div class="pico-modal-header">'+Object(s.c)("transform")+"</div><p>"+i+'</p><div class="jsoneditor-jmespath-label">'+Object(s.c)("transformWizardLabel")+' </div><div id="wizard" class="jsoneditor-jmespath-block jsoneditor-jmespath-wizard">  <table class="jsoneditor-jmespath-wizard-table">    <tbody>      <tr>        <th>'+Object(s.c)("transformWizardFilter")+'</th>        <td class="jsoneditor-jmespath-filter">          <div class="jsoneditor-inline jsoneditor-jmespath-filter-field" >            <select id="filterField">            </select>          </div>          <div class="jsoneditor-inline jsoneditor-jmespath-filter-relation" >            <select id="filterRelation">              <option value="==">==</option>              <option value="!=">!=</option>              <option value="<">&lt;</option>              <option value="<=">&lt;=</option>              <option value=">">&gt;</option>              <option value=">=">&gt;=</option>            </select>          </div>          <div class="jsoneditor-inline jsoneditor-jmespath-filter-value" >            <input type="text" class="value" placeholder="value..." id="filterValue" />          </div>        </td>      </tr>      <tr>        <th>'+Object(s.c)("transformWizardSortBy")+'</th>        <td class="jsoneditor-jmespath-filter">          <div class="jsoneditor-inline jsoneditor-jmespath-sort-field">            <select id="sortField">            </select>          </div>          <div class="jsoneditor-inline jsoneditor-jmespath-sort-order" >            <select id="sortOrder">              <option value="asc">Ascending</option>              <option value="desc">Descending</option>            </select>          </div>        </td>      </tr>      <tr id="selectFieldsPart">        <th>'+Object(s.c)("transformWizardSelectFields")+'</th>        <td class="jsoneditor-jmespath-filter">          <select class="jsoneditor-jmespath-select-fields" id="selectFields" multiple></select>        </td>      </tr>    </tbody>  </table></div><div class="jsoneditor-jmespath-label">'+Object(s.c)("transformQueryLabel")+' </div><div class="jsoneditor-jmespath-block">  <textarea id="query"             rows="4"             autocomplete="off"             autocorrect="off"             autocapitalize="off"             spellcheck="false"            title="'+Object(s.c)("transformQueryTitle")+'">[*]</textarea></div><div class="jsoneditor-jmespath-label">'+Object(s.c)("transformPreviewLabel")+' </div><div class="jsoneditor-jmespath-block">  <textarea id="preview"       class="jsoneditor-transform-preview"      readonly> </textarea></div><div class="jsoneditor-jmespath-block jsoneditor-modal-actions">  <input type="submit" id="ok" value="'+Object(s.c)("ok")+'" autofocus /></div></div>';r()({parent:t,content:o,overlayClass:"jsoneditor-modal-overlay",overlayStyles:{backgroundColor:"rgb(1,1,1)",opacity:.3},modalClass:"jsoneditor-modal jsoneditor-modal-transform",focus:!1}).afterCreate(function(t){var e=t.modalElem(),n=e.querySelector("#wizard"),i=e.querySelector("#ok"),o=e.querySelector("#filterField"),r=e.querySelector("#filterRelation"),s=e.querySelector("#filterValue"),a=e.querySelector("#sortField"),l=e.querySelector("#sortOrder"),c=e.querySelector("#selectFields"),d=e.querySelector("#query"),h=e.querySelector("#preview");Array.isArray(S)||(n.style.fontStyle="italic",n.textContent="(wizard not available for objects, only for arrays)"),Object(I.getChildPaths)(E).forEach(function(e){var t=b(e),n=document.createElement("option");n.text=t,n.value=t,o.appendChild(n);var i=document.createElement("option");i.text=t,i.value=t,a.appendChild(i)});var u,p=Object(I.getChildPaths)(E,!0).filter(function(e){return""!==e});0<p.length?p.forEach(function(e){var t=b(e),n=document.createElement("option");n.text=t,n.value=t,c.appendChild(n)}):(u=e.querySelector("#selectFieldsPart"))&&(u.style.display="none");var f=new N.a(o,{defaultSelected:!1,clearable:!0,allowDeselect:!0,placeholder:"field..."}),m=new N.a(r,{defaultSelected:!1,clearable:!0,allowDeselect:!0,placeholder:"compare..."}),v=new N.a(a,{defaultSelected:!1,clearable:!0,allowDeselect:!0,placeholder:"field..."}),g=new N.a(l,{defaultSelected:!1,clearable:!0,allowDeselect:!0,placeholder:"order..."}),y=new N.a(c,{multiple:!0,clearable:!0,defaultSelected:!1,placeholder:"select fields..."});function b(e){return""===e?"@":"."===e[0]?e.slice(1):e}f.on("selectr.change",_),m.on("selectr.change",_),s.oninput=_,v.on("selectr.change",_),g.on("selectr.change",_),y.on("selectr.change",_),e.querySelector(".pico-modal-contents").onclick=function(e){"A"!==e.target.nodeName&&e.preventDefault()};var x=Object(I.debounce)(function(){try{var e=T(S,d.value);h.className="jsoneditor-transform-preview",h.value=function(e,t,n){var i;"number"==typeof t?10<t?i=O(" ",10):1<=t&&(i=O(" ",t)):"string"==typeof t&&""!==t&&(i=t);var o,r,s=k(e,i,"",n);return s.length>n?(o=s,("number"==typeof(r=n)?o.slice(0,r):o)+"..."):s}(e,2,D.b),i.disabled=!1}catch(e){h.className="jsoneditor-transform-preview jsoneditor-error",h.value=e.toString(),i.disabled=!0}},300);function C(e,t){try{d.value=w(e,t),i.disabled=!1,x()}catch(e){var n='Error: an error happened when executing "createQuery": '+(e.message||e.toString());d.value="",i.disabled=!0,h.className="jsoneditor-transform-preview jsoneditor-error",h.value=n}}function _(){var e={};if(o.value&&r.value&&s.value&&(e.filter={field:o.value,relation:r.value,value:s.value}),a.value&&l.value&&(e.sort={field:a.value,direction:l.value}),c.value){for(var t,n=[],i=0;i<c.options.length;i++){c.options[i].selected&&(t=c.options[i].value,n.push(t))}e.projection={fields:n}}C(E,e)}d.oninput=x,i.onclick=function(e){e.preventDefault(),e.stopPropagation(),t.close(),j(d.value)},C(E,{}),setTimeout(function(){d.select(),d.focus(),d.selectionStart=3,d.selectionEnd=3})}).afterClose(function(e){e.destroy()}).show()}},function(e,t,n){"use strict";n.d(t,"a",function(){return o});var f=n(3),m=n(1);function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var o=function(){function p(e,t,n,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,p);for(var o={code:{text:Object(m.c)("modeCodeText"),title:Object(m.c)("modeCodeTitle"),click:function(){i("code")}},form:{text:Object(m.c)("modeFormText"),title:Object(m.c)("modeFormTitle"),click:function(){i("form")}},text:{text:Object(m.c)("modeTextText"),title:Object(m.c)("modeTextTitle"),click:function(){i("text")}},tree:{text:Object(m.c)("modeTreeText"),title:Object(m.c)("modeTreeTitle"),click:function(){i("tree")}},view:{text:Object(m.c)("modeViewText"),title:Object(m.c)("modeViewTitle"),click:function(){i("view")}},preview:{text:Object(m.c)("modePreviewText"),title:Object(m.c)("modePreviewTitle"),click:function(){i("preview")}}},r=[],s=0;s<t.length;s++){var a=t[s],l=o[a];if(!l)throw new Error('Unknown mode "'+a+'"');l.className="jsoneditor-type-modes"+(n===a?" jsoneditor-selected":""),r.push(l)}var c=o[n];if(!c)throw new Error('Unknown mode "'+n+'"');var d=c.text,h=document.createElement("button");h.type="button",h.className="jsoneditor-modes jsoneditor-separator",h.textContent=d+" ▾",h.title=Object(m.c)("modeEditorTitle"),h.onclick=function(){new f.a(r).show(h,e)};var u=document.createElement("div");u.className="jsoneditor-modes",u.style.position="relative",u.appendChild(h),e.appendChild(u),this.dom={container:e,box:h,frame:u}}var e,t,n;return e=p,(t=[{key:"focus",value:function(){this.dom.box.focus()}},{key:"destroy",value:function(){this.dom&&this.dom.frame&&this.dom.frame.parentNode&&this.dom.frame.parentNode.removeChild(this.dom.frame),this.dom=null}}])&&i(e.prototype,t),n&&i(e,n),p}()},function(e,t,n){"use strict";function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}n.d(t,"a",function(){return i});var i=function(){function t(e){if(!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),this.target=e.target||null,!this.target)throw new Error('FocusTracker constructor called without a "target" to track.');this.onFocus="function"==typeof e.onFocus?e.onFocus:null,this.onBlur="function"==typeof e.onBlur?e.onBlur:null,this._onClick=this._onEvent.bind(this),this._onKeyUp=function(e){9!==e.which&&9!==e.keyCode||this._onEvent(e)}.bind(this),this.focusFlag=!1,this.firstEventFlag=!0,(this.onFocus||this.onBlur)&&(document.addEventListener("click",this._onClick),document.addEventListener("keyup",this._onKeyUp))}var e,n,i;return e=t,(n=[{key:"destroy",value:function(){document.removeEventListener("click",this._onClick),document.removeEventListener("keyup",this._onKeyUp),this._onEvent({target:document.body})}},{key:"_onEvent",value:function(e){var t=e.target,n=t===this.target||!(!this.target.contains(t)&&!this.target.contains(document.activeElement));n?this.focusFlag||(this.onFocus&&this.onFocus({type:"focus",target:this.target}),this.focusFlag=!0):(this.focusFlag||this.firstEventFlag)&&(this.onBlur&&this.onBlur({type:"blur",target:this.target}),this.focusFlag=!1,this.firstEventFlag&&(this.firstEventFlag=!1))}}])&&o(e.prototype,n),i&&o(e,i),t}()},function(e,t,n){"use strict";function i(){}var o={defaultSelected:!0,width:"auto",disabled:!1,searchable:!0,clearable:!1,sortSelected:!1,allowDeselect:!1,closeOnScroll:!1,nativeDropdown:!1,placeholder:"Select an option...",taggable:!1,tagPlaceholder:"Enter a tag..."};i.prototype={on:function(e,t){this._events=this._events||{},this._events[e]=this._events[e]||[],this._events[e].push(t)},off:function(e,t){this._events=this._events||{},e in this._events!=!1&&this._events[e].splice(this._events[e].indexOf(t),1)},emit:function(e){if(this._events=this._events||{},e in this._events!=!1)for(var t=0;t<this._events[e].length;t++)this._events[e][t].apply(this,Array.prototype.slice.call(arguments,1))}},i.mixin=function(e){for(var t=["on","off","emit"],n=0;n<t.length;n++)"function"==typeof e?e.prototype[t[n]]=i.prototype[t[n]]:e[t[n]]=i.prototype[t[n]];return e};var l={extend:function(e,t){var n;for(n in t=t||{},e)e.hasOwnProperty(n)&&(t.hasOwnProperty(n)||(t[n]=e[n]));return t},each:function(e,t,n){if("[object Object]"===Object.prototype.toString.call(e))for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(n,i,e[i],e);else for(var o=0,r=e.length;o<r;o++)t.call(n,o,e[o],e)},createElement:function(e,t){var n,i,o=document,r=o.createElement(e);if(t&&"[object Object]"===Object.prototype.toString.call(t))for(n in t){n in r?r[n]=t[n]:"html"===n?r.textContent=t[n]:"text"===n?(i=o.createTextNode(t[n]),r.appendChild(i)):r.setAttribute(n,t[n])}return r},hasClass:function(e,t){if(e)return e.classList?e.classList.contains(t):!!e.className&&!!e.className.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))},addClass:function(e,t){l.hasClass(e,t)||(e.classList?e.classList.add(t):e.className=e.className.trim()+" "+t)},removeClass:function(e,t){l.hasClass(e,t)&&(e.classList?e.classList.remove(t):e.className=e.className.replace(new RegExp("(^|\\s)"+t.split(" ").join("|")+"(\\s|$)","gi")," "))},closest:function(e,t){return e&&e!==document.body&&(t(e)?e:l.closest(e.parentNode,t))},isInt:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},debounce:function(i,o,r){var s;return function(){var e=this,t=arguments,n=r&&!s;clearTimeout(s),s=setTimeout(function(){s=null,r||i.apply(e,t)},o),n&&i.apply(e,t)}},rect:function(e,t){var n=window,i=e.getBoundingClientRect(),o=t?n.pageXOffset:0,r=t?n.pageYOffset:0;return{bottom:i.bottom+r,height:i.height,left:i.left+o,right:i.right+o,top:i.top+r,width:i.width}},includes:function(e,t){return-1<e.indexOf(t)},truncate:function(e){for(;e.firstChild;)e.removeChild(e.firstChild)}};function c(e,t){return e.hasOwnProperty(t)&&(!0===e[t]||e[t].length)}function a(e,t,n){e.parentNode?e.parentNode.parentNode||t.appendChild(e.parentNode):t.appendChild(e),l.removeClass(e,"excluded"),n||(e.textContent=e.textContent+"")}function d(){var n,e;this.items.length&&(n=document.createDocumentFragment(),this.config.pagination?(e=this.pages.slice(0,this.pageIndex),l.each(e,function(e,t){l.each(t,function(e,t){a(t,n,this.customOption)},this)},this)):l.each(this.items,function(e,t){a(t,n,this.customOption)},this),n.childElementCount&&(l.removeClass(this.items[this.navIndex],"active"),this.navIndex=n.querySelector(".selectr-option").idx,l.addClass(this.items[this.navIndex],"active")),this.tree.appendChild(n))}function h(e,t){t=t||e;var n=this.customOption?this.config.renderOption(t):e.textContent,i=l.createElement("li",{class:"selectr-option",html:n,role:"treeitem","aria-selected":!1});return i.idx=e.idx,this.items.push(i),e.defaultSelected&&this.defaultSelected.push(e.idx),e.disabled&&(i.disabled=!0,l.addClass(i,"disabled")),i}function r(){(this.config.searchable||this.config.taggable)&&(this.input.value=null,this.searching=!1,this.config.searchable&&l.removeClass(this.inputContainer,"active"),l.hasClass(this.container,"notice")&&(l.removeClass(this.container,"notice"),l.addClass(this.container,"open"),this.input.focus()),l.each(this.items,function(e,t){l.removeClass(t,"excluded"),this.customOption||(t.textContent=t.textContent+"")},this))}function s(e,t){if(t=t||{},!e)throw new Error("You must supply either a HTMLSelectElement or a CSS3 selector string.");if("string"==typeof(this.el=e)&&(this.el=document.querySelector(e)),null===this.el)throw new Error("The element you passed to Selectr can not be found.");if("select"!==this.el.nodeName.toLowerCase())throw new Error("The element you passed to Selectr is not a HTMLSelectElement.");this.render(t)}var u=function(){var n,e=this.tree,t=e.scrollTop;e.scrollHeight-e.offsetHeight<=t&&this.pageIndex<this.pages.length&&(n=document.createDocumentFragment(),l.each(this.pages[this.pageIndex],function(e,t){a(t,n,this.customOption)},this),e.appendChild(n),this.pageIndex++,this.emit("selectr.paginate",{items:this.items.length,total:this.data.length,page:this.pageIndex,pages:this.pages.length}))};s.prototype.render=function(e){var t;this.rendered||(this.config=l.extend(o,e),this.originalType=this.el.type,this.originalIndex=this.el.tabIndex,this.defaultSelected=[],this.originalOptionCount=this.el.options.length,(this.config.multiple||this.config.taggable)&&(this.el.multiple=!0),this.disabled=c(this.config,"disabled"),this.opened=!1,this.config.taggable&&(this.config.searchable=!1),this.navigating=!1,this.mobileDevice=!1,/Android|webOS|iPhone|iPad|BlackBerry|Windows Phone|Opera Mini|IEMobile|Mobile/i.test(navigator.userAgent)&&(this.mobileDevice=!0),this.customOption=this.config.hasOwnProperty("renderOption")&&"function"==typeof this.config.renderOption,this.customSelected=this.config.hasOwnProperty("renderSelection")&&"function"==typeof this.config.renderSelection,i.mixin(this),function(){this.requiresPagination=this.config.pagination&&0<this.config.pagination,c(this.config,"width")&&(l.isInt(this.config.width)?this.width=this.config.width+"px":"auto"===this.config.width?this.width="100%":l.includes(this.config.width,"%")&&(this.width=this.config.width)),this.container=l.createElement("div",{class:"selectr-container"}),this.config.customClass&&l.addClass(this.container,this.config.customClass),this.mobileDevice?l.addClass(this.container,"selectr-mobile"):l.addClass(this.container,"selectr-desktop"),this.el.tabIndex=-1,this.config.nativeDropdown||this.mobileDevice?l.addClass(this.el,"selectr-visible"):l.addClass(this.el,"selectr-hidden"),this.selected=l.createElement("div",{class:"selectr-selected",disabled:this.disabled,tabIndex:1,"aria-expanded":!1}),this.label=l.createElement(this.el.multiple?"ul":"span",{class:"selectr-label"});var e,t=l.createElement("div",{class:"selectr-options-container"});this.tree=l.createElement("ul",{class:"selectr-options",role:"tree","aria-hidden":!0,"aria-expanded":!1}),this.notice=l.createElement("div",{class:"selectr-notice"}),this.el.setAttribute("aria-hidden",!0),this.disabled&&(this.el.disabled=!0),this.el.multiple&&(l.addClass(this.label,"selectr-tags"),l.addClass(this.container,"multiple"),this.tags=[],this.selectedValues=this.getSelectedProperties("value"),this.selectedIndexes=this.getSelectedProperties("idx")),this.selected.appendChild(this.label),this.config.clearable&&(this.selectClear=l.createElement("button",{class:"selectr-clear",type:"button"}),this.container.appendChild(this.selectClear),l.addClass(this.container,"clearable")),this.config.taggable&&(e=l.createElement("li",{class:"input-tag"}),this.input=l.createElement("input",{class:"selectr-tag-input",placeholder:this.config.tagPlaceholder,tagIndex:0,autocomplete:"off",autocorrect:"off",autocapitalize:"off",spellcheck:"false",role:"textbox",type:"search"}),e.appendChild(this.input),this.label.appendChild(e),l.addClass(this.container,"taggable"),this.tagSeperators=[","],this.config.tagSeperators&&(this.tagSeperators=this.tagSeperators.concat(this.config.tagSeperators))),this.config.searchable&&(this.input=l.createElement("input",{class:"selectr-input",tagIndex:-1,autocomplete:"off",autocorrect:"off",autocapitalize:"off",spellcheck:"false",role:"textbox",type:"search"}),this.inputClear=l.createElement("button",{class:"selectr-input-clear",type:"button"}),this.inputContainer=l.createElement("div",{class:"selectr-input-container"}),this.inputContainer.appendChild(this.input),this.inputContainer.appendChild(this.inputClear),t.appendChild(this.inputContainer)),t.appendChild(this.notice),t.appendChild(this.tree),this.items=[],this.options=[],this.el.options.length&&(this.options=[].slice.call(this.el.options));var n,i,o,r=!1,s=0;this.el.children.length&&l.each(this.el.children,function(e,t){"OPTGROUP"===t.nodeName?(r=l.createElement("ul",{class:"selectr-optgroup",role:"group",html:"<li class='selectr-optgroup--label'>"+t.label+"</li>"}),l.each(t.children,function(e,t){t.idx=s,r.appendChild(h.call(this,t,r)),s++},this)):(t.idx=s,h.call(this,t),s++)},this),this.config.data&&Array.isArray(this.config.data)&&(n=!(this.data=[]),r=!1,s=0,l.each(this.config.data,function(e,t){c(t,"children")?(n=l.createElement("optgroup",{label:t.text}),r=l.createElement("ul",{class:"selectr-optgroup",role:"group",html:"<li class='selectr-optgroup--label'>"+t.text+"</li>"}),l.each(t.children,function(e,t){(i=new Option(t.text,t.value,!1,t.hasOwnProperty("selected")&&!0===t.selected)).disabled=c(t,"disabled"),this.options.push(i),n.appendChild(i),i.idx=s,r.appendChild(h.call(this,i,t)),this.data[s]=t,s++},this)):((i=new Option(t.text,t.value,!1,t.hasOwnProperty("selected")&&!0===t.selected)).disabled=c(t,"disabled"),this.options.push(i),i.idx=s,h.call(this,i,t),this.data[s]=t,s++)},this)),this.setSelected(!0);for(
var a=this.navIndex=0;a<this.items.length;a++)if(o=this.items[a],!l.hasClass(o,"disabled")){l.addClass(o,"active"),this.navIndex=a;break}this.requiresPagination&&(this.pageIndex=1,this.paginate()),this.container.appendChild(this.selected),this.container.appendChild(t),this.placeEl=l.createElement("div",{class:"selectr-placeholder"}),this.setPlaceholder(),this.selected.appendChild(this.placeEl),this.disabled&&this.disable(),this.el.parentNode.insertBefore(this.container,this.el),this.container.appendChild(this.el)}.call(this),this.bindEvents(),this.update(),this.optsRect=l.rect(this.tree),this.rendered=!0,this.el.multiple||(this.el.selectedIndex=this.selectedIndex),t=this,setTimeout(function(){t.emit("selectr.init")},20))},s.prototype.getSelected=function(){return this.el.querySelectorAll("option:checked")},s.prototype.getSelectedProperties=function(t){var e=this.getSelected();return[].slice.call(e).map(function(e){return e[t]}).filter(function(e){return null!=e})},s.prototype.bindEvents=function(){var i=this;this.events={},this.events.dismiss=function(e){var t=e.target;this.container.contains(t)||!this.opened&&!l.hasClass(this.container,"notice")||this.close()}.bind(this),this.events.navigate=function(e){if(e=e||window.event,this.items.length&&this.opened&&l.includes([13,38,40],e.which)){if(e.preventDefault(),13===e.which)return!(this.config.taggable&&0<this.input.value.length)&&this.change(this.navIndex);var t,n=this.items[this.navIndex];switch(e.which){case 38:(t=0)<this.navIndex&&this.navIndex--;break;case 40:t=1,this.navIndex<this.items.length-1&&this.navIndex++}for(this.navigating=!0;l.hasClass(this.items[this.navIndex],"disabled")||l.hasClass(this.items[this.navIndex],"excluded");)if(t?this.navIndex++:this.navIndex--,this.searching){if(this.navIndex>this.tree.lastElementChild.idx){this.navIndex=this.tree.lastElementChild.idx;break}if(this.navIndex<this.tree.firstElementChild.idx){this.navIndex=this.tree.firstElementChild.idx;break}}var i=l.rect(this.items[this.navIndex]);t?(0===this.navIndex?this.tree.scrollTop=0:i.top+i.height>this.optsRect.top+this.optsRect.height&&(this.tree.scrollTop=this.tree.scrollTop+(i.top+i.height-(this.optsRect.top+this.optsRect.height))),this.navIndex===this.tree.childElementCount-1&&this.requiresPagination&&u.call(this)):0===this.navIndex?this.tree.scrollTop=0:i.top-this.optsRect.top<0&&(this.tree.scrollTop=this.tree.scrollTop+(i.top-this.optsRect.top)),n&&l.removeClass(n,"active"),l.addClass(this.items[this.navIndex],"active")}else this.navigating=!1}.bind(this),this.events.reset=this.reset.bind(this),(this.config.nativeDropdown||this.mobileDevice)&&(this.container.addEventListener("touchstart",function(e){e.changedTouches[0].target===i.el&&i.toggle()}),(this.config.nativeDropdown||this.mobileDevice)&&this.container.addEventListener("click",function(e){e.preventDefault(),e.stopPropagation(),e.target===i.el&&i.toggle()}),this.el.addEventListener("change",function(e){var t,n;i.el.multiple?(t=i.getSelectedProperties("idx"),n=function(e,t){for(var n,i=[],o=e.slice(0),r=0;r<t.length;r++)-1<(n=o.indexOf(t[r]))?o.splice(n,1):i.push(t[r]);return[i,o]}(i.selectedIndexes,t),l.each(n[0],function(e,t){i.select(t)},i),l.each(n[1],function(e,t){i.deselect(t)},i)):-1<i.el.selectedIndex&&i.select(i.el.selectedIndex)})),this.config.nativeDropdown&&this.container.addEventListener("keydown",function(e){"Enter"===e.key&&i.selected===document.activeElement&&(i.toggle(),setTimeout(function(){i.el.focus()},200))}),this.selected.addEventListener("click",function(e){i.disabled||i.toggle(),e.preventDefault(),e.stopPropagation()}),this.label.addEventListener("click",function(e){l.hasClass(e.target,"selectr-tag-remove")&&i.deselect(e.target.parentNode.idx)}),this.selectClear&&this.selectClear.addEventListener("click",this.clear.bind(this)),this.tree.addEventListener("mousedown",function(e){e.preventDefault()}),this.tree.addEventListener("click",function(e){e.preventDefault(),e.stopPropagation();var t=l.closest(e.target,function(e){return e&&l.hasClass(e,"selectr-option")});t&&(l.hasClass(t,"disabled")||(l.hasClass(t,"selected")?(i.el.multiple||!i.el.multiple&&i.config.allowDeselect)&&i.deselect(t.idx):i.select(t.idx),i.opened&&!i.el.multiple&&i.close()))}),this.tree.addEventListener("mouseover",function(e){l.hasClass(e.target,"selectr-option")&&(l.hasClass(e.target,"disabled")||(l.removeClass(i.items[i.navIndex],"active"),l.addClass(e.target,"active"),i.navIndex=[].slice.call(i.items).indexOf(e.target)))}),this.config.searchable&&(this.input.addEventListener("focus",function(e){i.searching=!0}),this.input.addEventListener("blur",function(e){i.searching=!1}),this.input.addEventListener("keyup",function(e){i.search(),i.config.taggable||(this.value.length?l.addClass(this.parentNode,"active"):l.removeClass(this.parentNode,"active"))}),this.inputClear.addEventListener("click",function(e){i.input.value=null,r.call(i),i.tree.childElementCount||d.call(i)})),this.config.taggable&&this.input.addEventListener("keyup",function(e){var n;i.search(),i.config.taggable&&this.value.length&&(n=this.value.trim(),13!==e.which&&!l.includes(i.tagSeperators,e.key)||(l.each(i.tagSeperators,function(e,t){n=n.replace(t,"")}),i.add({value:n,text:n,selected:!0},!0)?(i.close(),r.call(i)):(this.value="",i.setMessage("That tag is already in use."))))}),this.update=l.debounce(function(){i.opened&&i.config.closeOnScroll&&i.close(),i.width&&(i.container.style.width=i.width),i.invert()},50),this.requiresPagination&&(this.paginateItems=l.debounce(function(){u.call(this)},50),this.tree.addEventListener("scroll",this.paginateItems.bind(this))),document.addEventListener("click",this.events.dismiss),window.addEventListener("keydown",this.events.navigate),window.addEventListener("resize",this.update),window.addEventListener("scroll",this.update),this.el.form&&this.el.form.addEventListener("reset",this.events.reset)},s.prototype.setSelected=function(e){var n;this.config.data||this.el.multiple||!this.el.options.length||(0===this.el.selectedIndex&&(this.el.options[0].defaultSelected||this.config.defaultSelected||(this.el.selectedIndex=-1)),this.selectedIndex=this.el.selectedIndex,-1<this.selectedIndex&&this.select(this.selectedIndex)),this.config.multiple&&"select-one"===this.originalType&&!this.config.data&&this.el.options[0].selected&&!this.el.options[0].defaultSelected&&(this.el.options[0].selected=!1),l.each(this.options,function(e,t){t.selected&&t.defaultSelected&&this.select(t.idx)},this),this.config.selectedValue&&this.setValue(this.config.selectedValue),this.config.data&&(!this.el.multiple&&this.config.defaultSelected&&this.el.selectedIndex<0&&this.select(0),n=0,l.each(this.config.data,function(e,t){c(t,"children")?l.each(t.children,function(e,t){t.hasOwnProperty("selected")&&!0===t.selected&&this.select(n),n++},this):(t.hasOwnProperty("selected")&&!0===t.selected&&this.select(n),n++)},this))},s.prototype.destroy=function(){this.rendered&&(this.emit("selectr.destroy"),"select-one"===this.originalType&&(this.el.multiple=!1),this.config.data&&(this.el.textContent=""),l.removeClass(this.el,"selectr-hidden"),this.el.form&&l.off(this.el.form,"reset",this.events.reset),l.off(document,"click",this.events.dismiss),l.off(document,"keydown",this.events.navigate),l.off(window,"resize",this.update),l.off(window,"scroll",this.update),this.container.parentNode.replaceChild(this.el,this.container),this.rendered=!1)},s.prototype.change=function(e){var t=this.items[e],n=this.options[e];n.disabled||(n.selected&&l.hasClass(t,"selected")?this.deselect(e):this.select(e),this.opened&&!this.el.multiple&&this.close())},s.prototype.select=function(i){var e=this.items[i],t=[].slice.call(this.el.options),n=this.options[i];if(this.el.multiple){if(l.includes(this.selectedIndexes,i))return!1;if(this.config.maxSelections&&this.tags.length===this.config.maxSelections)return this.setMessage("A maximum of "+this.config.maxSelections+" items can be selected.",!0),!1;this.selectedValues.push(n.value),this.selectedIndexes.push(i),function(e){var t,c,d=this,n=document.createDocumentFragment(),i=this.options[e.idx],o=this.data?this.data[e.idx]:i,r=this.customSelected?this.config.renderSelection(o):i.textContent,s=l.createElement("li",{class:"selectr-tag",html:r}),a=l.createElement("button",{class:"selectr-tag-remove",type:"button"});s.appendChild(a),s.idx=e.idx,s.tag=i.value,this.tags.push(s),this.config.sortSelected?(t=this.tags.slice(),c=function(e,i){e.replace(/(\d+)|(\D+)/g,function(e,t,n){i.push([t||1/0,n||""])})},t.sort(function(e,t){var n,i,o=[],r=[];for(!0===d.config.sortSelected?(n=e.tag,i=t.tag):"text"===d.config.sortSelected&&(n=e.textContent,i=t.textContent),c(n,o),c(i,r);o.length&&r.length;){var s=o.shift(),a=r.shift(),l=s[0]-a[0]||s[1].localeCompare(a[1]);if(l)return l}return o.length-r.length}),l.each(t,function(e,t){n.appendChild(t)}),this.label.textContent=""):n.appendChild(s),this.config.taggable?this.label.insertBefore(n,this.input.parentNode):this.label.appendChild(n)}.call(this,e)}else{var o=this.data?this.data[i]:n;this.label.textContent=this.customSelected?this.config.renderSelection(o):n.textContent,this.selectedValue=n.value,this.selectedIndex=i,l.each(this.options,function(e,t){var n=this.items[e];e!==i&&(n&&l.removeClass(n,"selected"),t.selected=!1,t.removeAttribute("selected"))},this)}l.includes(t,n)||this.el.add(n),e.setAttribute("aria-selected",!0),l.addClass(e,"selected"),l.addClass(this.container,"has-selected"),n.selected=!0,n.setAttribute("selected",""),this.emit("selectr.change",n),this.emit("selectr.select",n)},s.prototype.deselect=function(e,t){var n=this.items[e],i=this.options[e];if(this.el.multiple){var o=this.selectedIndexes.indexOf(e);this.selectedIndexes.splice(o,1);var r=this.selectedValues.indexOf(i.value);this.selectedValues.splice(r,1),function(n){var i=!1;l.each(this.tags,function(e,t){t.idx===n.idx&&(i=t)},this),i&&(this.label.removeChild(i),this.tags.splice(this.tags.indexOf(i),1))}.call(this,n),this.tags.length||l.removeClass(this.container,"has-selected")}else{if(!t&&!this.config.clearable&&!this.config.allowDeselect)return!1;this.label.textContent="",this.selectedValue=null,this.el.selectedIndex=this.selectedIndex=-1,l.removeClass(this.container,"has-selected")}this.items[e].setAttribute("aria-selected",!1),l.removeClass(this.items[e],"selected"),i.selected=!1,i.removeAttribute("selected"),this.emit("selectr.change",null),this.emit("selectr.deselect",i)},s.prototype.setValue=function(n){var i=Array.isArray(n);if(i||(n=n.toString().trim()),!this.el.multiple&&i)return!1;l.each(this.options,function(e,t){(i&&l.includes(n.toString(),t.value)||t.value===n)&&this.change(t.idx)},this)},s.prototype.getValue=function(e,t){var n,i;return this.el.multiple?e?this.selectedIndexes.length&&(i={values:[]},l.each(this.selectedIndexes,function(e,t){var n=this.options[t];i.values[e]={value:n.value,text:n.textContent}},this)):i=this.selectedValues.slice():i=e?{value:(n=this.options[this.selectedIndex]).value,text:n.textContent}:this.selectedValue,e&&t&&(i=JSON.stringify(i)),i},s.prototype.add=function(n,i){if(n){if(this.data=this.data||[],this.items=this.items||[],this.options=this.options||[],Array.isArray(n))l.each(n,function(e,t){this.add(t,i)},this);else if("[object Object]"===Object.prototype.toString.call(n)){if(i){var o=!1;if(l.each(this.options,function(e,t){t.value.toLowerCase()===n.value.toLowerCase()&&(o=!0)}),o)return!1}var e=l.createElement("option",n);return this.data.push(n),this.options.push(e),e.idx=0<this.options.length?this.options.length-1:0,h.call(this,e),n.selected&&this.select(e.idx),e}return this.setPlaceholder(),this.config.pagination&&this.paginate(),!0}},s.prototype.remove=function(n){var i,o=[];Array.isArray(n)?l.each(n,function(e,t){l.isInt(t)?o.push(this.getOptionByIndex(t)):"string"==typeof n&&o.push(this.getOptionByValue(t))},this):l.isInt(n)?o.push(this.getOptionByIndex(n)):"string"==typeof n&&o.push(this.getOptionByValue(n)),o.length&&(l.each(o,function(e,t){i=t.idx,this.el.remove(t),this.options.splice(i,1);var n=this.items[i].parentNode;n&&n.removeChild(this.items[i]),this.items.splice(i,1),l.each(this.options,function(e,t){t.idx=e,this.items[e].idx=e},this)},this),this.setPlaceholder(),this.config.pagination&&this.paginate())},s.prototype.removeAll=function(){this.clear(!0),l.each(this.el.options,function(e,t){this.el.remove(t)},this),l.truncate(this.tree),this.items=[],this.options=[],this.data=[],this.navIndex=0,this.requiresPagination&&(this.requiresPagination=!1,this.pageIndex=1,this.pages=[]),this.setPlaceholder()},s.prototype.search=function(r){var s,e,t;this.navigating||(r=r||this.input.value,s=document.createDocumentFragment(),this.removeMessage(),l.truncate(this.tree),1<r.length?(l.each(this.options,function(e,t){var n,i,o=this.items[t.idx];l.includes(t.textContent.toLowerCase(),r.toLowerCase())&&!t.disabled?(a(o,s,this.customOption),l.removeClass(o,"excluded"),this.customOption||(o.textContent="",(n=function(e,t){var n=new RegExp(e,"i").exec(t);if(n){var i=n.index,o=n.index+n[0].length;return{before:t.substring(0,i),match:t.substring(i,o),after:t.substring(o)}}return null}(r,t.textContent))&&(o.appendChild(document.createTextNode(n.before)),(i=document.createElement("span")).className="selectr-match",i.appendChild(document.createTextNode(n.match)),o.appendChild(i),o.appendChild(document.createTextNode(n.after))))):l.addClass(o,"excluded")},this),s.childElementCount?(e=this.items[this.navIndex],t=s.firstElementChild,l.removeClass(e,"active"),this.navIndex=t.idx,l.addClass(t,"active")):this.config.taggable||this.setMessage("no results.")):d.call(this),this.tree.appendChild(s))},s.prototype.toggle=function(){this.disabled||(this.opened?this.close():this.open())},s.prototype.open=function(){var e=this;return!!this.options.length&&(this.opened||this.emit("selectr.open"),this.opened=!0,this.mobileDevice||this.config.nativeDropdown?(l.addClass(this.container,"native-open"),void(this.config.data&&l.each(this.options,function(e,t){this.el.add(t)},this))):(l.addClass(this.container,"open"),d.call(this),this.invert(),this.tree.scrollTop=0,l.removeClass(this.container,"notice"),this.selected.setAttribute("aria-expanded",!0),this.tree.setAttribute("aria-hidden",!1),this.tree.setAttribute("aria-expanded",!0),void(this.config.searchable&&!this.config.taggable&&setTimeout(function(){e.input.focus(),e.input.tabIndex=0},10))))},s.prototype.close=function(){var e;this.opened&&this.emit("selectr.close"),this.opened=!1,this.mobileDevice||this.config.nativeDropdown?l.removeClass(this.container,"native-open"):(e=l.hasClass(this.container,"notice"),this.config.searchable&&!e&&(this.input.blur(),this.input.tabIndex=-1,this.searching=!1),e&&(l.removeClass(this.container,"notice"),this.notice.textContent=""),l.removeClass(this.container,"open"),l.removeClass(this.container,"native-open"),this.selected.setAttribute("aria-expanded",!1),this.tree.setAttribute("aria-hidden",!0),this.tree.setAttribute("aria-expanded",!1),l.truncate(this.tree),r.call(this))},s.prototype.enable=function(){this.disabled=!1,this.el.disabled=!1,this.selected.tabIndex=this.originalIndex,this.el.multiple&&l.each(this.tags,function(e,t){t.lastElementChild.tabIndex=0}),l.removeClass(this.container,"selectr-disabled")},s.prototype.disable=function(e){e||(this.el.disabled=!0),this.selected.tabIndex=-1,this.el.multiple&&l.each(this.tags,function(e,t){t.lastElementChild.tabIndex=-1}),this.disabled=!0,l.addClass(this.container,"selectr-disabled")},s.prototype.reset=function(){this.disabled||(this.clear(),this.setSelected(!0),l.each(this.defaultSelected,function(e,t){this.select(t)},this),this.emit("selectr.reset"))},s.prototype.clear=function(e){var t;this.el.multiple?this.selectedIndexes.length&&(t=this.selectedIndexes.slice(),l.each(t,function(e,t){this.deselect(t)},this)):-1<this.selectedIndex&&this.deselect(this.selectedIndex,e),this.emit("selectr.clear")},s.prototype.serialise=function(e){var i=[];return l.each(this.options,function(e,t){var n={value:t.value,text:t.textContent};t.selected&&(n.selected=!0),t.disabled&&(n.disabled=!0),i[e]=n}),e?JSON.stringify(i):i},s.prototype.serialize=function(e){return this.serialise(e)},s.prototype.setPlaceholder=function(e){e=e||this.config.placeholder||this.el.getAttribute("placeholder"),this.options.length||(e="No options available"),this.placeEl.textContent=e},s.prototype.paginate=function(){if(this.items.length){var n=this;return this.pages=this.items.map(function(e,t){return t%n.config.pagination==0?n.items.slice(t,t+n.config.pagination):null}).filter(function(e){return e}),this.pages}},s.prototype.setMessage=function(e,t){t&&this.close(),l.addClass(this.container,"notice"),this.notice.textContent=e},s.prototype.removeMessage=function(){l.removeClass(this.container,"notice"),this.notice.textContent=""},s.prototype.invert=function(){var e=l.rect(this.selected),t=this.tree.parentNode.offsetHeight,n=window.innerHeight;e.top+e.height+t>n?(l.addClass(this.container,"inverted"),this.isInverted=!0):(l.removeClass(this.container,"inverted"),this.isInverted=!1),this.optsRect=l.rect(this.tree)},s.prototype.getOptionByIndex=function(e){return this.options[e]},s.prototype.getOptionByValue=function(e){for(var t=!1,n=0,i=this.options.length;n<i;n++)if(this.options[n].value.trim()===e.toString().trim()){t=this.options[n];break}return t},e.exports=s},function(e,t){e.exports=function t(e,n){"use strict";function i(e){return t.insensitive&&(""+e).toLowerCase()||""+e}var o,r,s=/(^([+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,a=/(^[ ]*|[ ]*$)/g,l=/(^([\w ]+,?[\w ]+)?[\w ]+,?[\w ]+\d+:\d+(:\d+)?[\w ]?|^\d{1,4}[\/\-]\d{1,4}[\/\-]\d{1,4}|^\w+, \w+ \d+, \d{4})/,c=/^0x[0-9a-f]+$/i,d=/^0/,h=i(e).replace(a,"")||"",u=i(n).replace(a,"")||"",p=h.replace(s,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),f=u.replace(s,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),m=parseInt(h.match(c),16)||1!==p.length&&h.match(l)&&Date.parse(h),v=parseInt(u.match(c),16)||m&&u.match(l)&&Date.parse(u)||null;if(v){if(m<v)return-1;if(v<m)return 1}for(var g=0,y=Math.max(p.length,f.length);g<y;g++){if(o=!(p[g]||"").match(d)&&parseFloat(p[g])||p[g]||0,r=!(f[g]||"").match(d)&&parseFloat(f[g])||f[g]||0,isNaN(o)!==isNaN(r))return isNaN(o)?1:-1;if(typeof o!=typeof r&&(o+="",r+=""),o<r)return-1;if(r<o)return 1}return 0}},function(e,t,n){"use strict";n.d(t,"a",function(){return i});var p=n(0);function i(o,e,r){var t,n,i=3<arguments.length&&void 0!==arguments[3]&&arguments[3],s="function"==typeof(t=o).getRootNode?t.getRootNode():window,a={},l=o.getBoundingClientRect(),c=e.getBoundingClientRect(),d=document.createElement("div");function h(){if(d&&d.parentNode){for(var e in d.parentNode.removeChild(d),a){var t;n=a,i=e,Object.prototype.hasOwnProperty.call(n,i)&&((t=a[e])&&Object(p.removeEventListener)(s,e,t),delete a[e])}"function"==typeof r&&r(o)}var n,i}function u(e){var t;(t=e.target)===d||Object(p.isChildOf)(t,d)||h()}return d.className="jsoneditor-anchor",d.style.position="absolute",d.style.left=l.left-c.left+"px",d.style.top=l.top-c.top+"px",d.style.width=l.width-2+"px",d.style.height=l.height-2+"px",d.style.boxSizing="border-box",e.appendChild(d),a.mousedown=Object(p.addEventListener)(s,"mousedown",u),a.mousewheel=Object(p.addEventListener)(s,"mousewheel",u),i&&(n=null,d.onmouseover=function(){clearTimeout(n),n=null},d.onmouseout=function(){n=n||setTimeout(h,200)}),d.destroy=h,d}},function(e,t,n){var i;if(window.Picker)i=window.Picker;else try{i=n(!function(){var e=new Error("Cannot find module 'vanilla-picker'");throw e.code="MODULE_NOT_FOUND",e}())}catch(e){}e.exports=i},function(e,t,n){var i,o,r;!function(){"use strict";o=[],void 0===(r="function"==typeof(i=function(){"use strict";function j(e){if(typeof Node==="object"){return e instanceof Node}else{return e&&typeof e==="object"&&typeof e.nodeType==="number"}}function e(e){return"string"==typeof e}function S(){var r=[];return{watch:r.push.bind(r),trigger:function(e,t){var n=true;var i={detail:t,preventDefault:function e(){n=false}};for(var o=0;o<r.length;o++){r[o](e,i)}return n}}}function N(e){return window.getComputedStyle(e).display==="none"}function k(e){this.elem=e}function t(e,t){return k.make(e("parent")).clazz("pico-overlay").clazz(e("overlayClass","")).stylize({display:"none",position:"fixed",top:"0px",left:"0px",height:"100%",width:"100%",zIndex:1e4}).stylize(e("overlayStyles",{opacity:.5,background:"#000"})).onClick(function(){e("overlayClose",!0)&&t()})}k.make=function(e,t){if(typeof e==="string"){e=document.querySelector(e)}var n=document.createElement(t||"div");(e||document.body).appendChild(n);return new k(n)},k.prototype={child:function(e){return k.make(this.elem,e)},stylize:function(e){e=e||{};if(typeof e.opacity!=="undefined"){e.filter="alpha(opacity="+e.opacity*100+")"}for(var t in e){if(e.hasOwnProperty(t)){this.elem.style[t]=e[t]}}return this},clazz:function(e){this.elem.className+=" "+e;return this},html:function(e){if(j(e)){this.elem.appendChild(e)}else{this.elem.innerHTML=e}return this},onClick:function(e){this.elem.addEventListener("click",e);return this},destroy:function(){this.elem.parentNode.removeChild(this.elem)},hide:function(){this.elem.style.display="none"},show:function(){this.elem.style.display="block"},attr:function(e,t){if(t!==undefined){this.elem.setAttribute(e,t)}return this},anyAncestor:function(e){var t=this.elem;while(t){if(e(new k(t))){return true}else{t=t.parentNode}}return false},isVisible:function(){return!N(this.elem)}};var O=1;function n(e,t){var n=e("width","auto");if("number"==typeof n)n+="px";var i=e("modalId","pico-"+O++);return k.make(e("parent")).clazz("pico-content").clazz(e("modalClass","")).stylize({display:"none",position:"fixed",zIndex:10001,left:"50%",top:"38.1966%",maxHeight:"90%",boxSizing:"border-box",width:n,"-ms-transform":"translate(-50%,-38.1966%)","-moz-transform":"translate(-50%,-38.1966%)","-webkit-transform":"translate(-50%,-38.1966%)","-o-transform":"translate(-50%,-38.1966%)",transform:"translate(-50%,-38.1966%)"}).stylize(e("modalStyles",{overflow:"auto",backgroundColor:"white",padding:"20px",borderRadius:"5px"})).html(e("content")).attr("id",i).attr("role","dialog").attr("aria-labelledby",e("ariaLabelledBy")).attr("aria-describedby",e("ariaDescribedBy",i)).onClick(function(e){new k(e.target).anyAncestor(function(e){return/\bpico-close\b/.test(e.elem.className)})&&t()})}function i(e,t){if(t("closeButton",!0))return e.child("button").html(t("closeHtml","&#xD7;")).clazz("pico-close").clazz(t("closeClass","")).stylize(t("closeStyles",{borderRadius:"2px",border:0,padding:0,cursor:"pointer",height:"15px",width:"15px",position:"absolute",top:"5px",right:"5px",fontSize:"16px",textAlign:"center",lineHeight:"15px",background:"#CCC"})).attr("aria-label",t("close-label","Close"))}function I(e){return function(){return e().elem}}var D=S(),A=S();function o(e,t){function E(e,t){return(e.msMatchesSelector||e.webkitMatchesSelector||e.matches).call(e,t)}function w(e){return!(N(e)||E(e,":disabled")||e.hasAttribute("contenteditable"))&&(e.hasAttribute("tabindex")||E(e,"input,select,textarea,button,a[href],area[href],iframe"))}function T(e){for(var t=e.getElementsByTagName("*"),n=0;n<t.length;n++)if(w(t[n]))return t[n]}function r(e){for(var t=e.getElementsByTagName("*"),n=t.length;n--;)if(w(t[n]))return t[n]}var e;d.beforeShow(function(){e=document.activeElement}),d.afterShow(function(){var e;!h()||(e=T(d.modalElem()))&&e.focus()}),d.afterClose(function(){h()&&e&&e.focus(),e=null}),A.watch(function(e){var t,n;h()&&d.isVisible()&&(t=T(d.modalElem()),n=function(e){for(var t=e.getElementsByTagName("*"),n=t.length;n--;)if(w(t[n]))return t[n]}(d.modalElem()),(e.shiftKey?t:n)===document.activeElement&&((e.shiftKey?n:t).focus(),e.preventDefault()))})}function r(e,t){var f,m=new k(document.body);u.beforeShow(function(){f=m.elem.style.overflow,p()&&m.stylize({overflow:"hidden"})}),u.afterClose(function(){m.stylize({overflow:f})})}return document.documentElement.addEventListener("keydown",function(e){var t=e.which||e.keyCode;27===t?D.trigger():9===t&&A.trigger(e)}),function(i){"string"!=typeof i&&!j(i)||(i={content:i});var r=S(),t=S(),n=S(),o=S(),s=S();function a(e,t){var n=i[e];return"function"==typeof n&&(n=n(t)),void 0===n?t:n}var l,c,d,h,e,u,p,f,m,v=_.bind(window,"modal"),g=_.bind(window,"overlay"),y=_.bind(window,"close");function b(e){g().hide(),v().hide(),s.trigger(l,e)}function x(e){o.trigger(l,e)&&b(e)}function C(e){return function(){return e.apply(this,arguments),l}}function _(e,t){var n,i,o;return c||(n=function(e,t){var n=e("width","auto");"number"==typeof n&&(n+="px");var i=e("modalId","pico-"+O++);return k.make(e("parent")).clazz("pico-content").clazz(e("modalClass","")).stylize({display:"none",position:"fixed",zIndex:10001,left:"50%",top:"38.1966%",maxHeight:"90%",boxSizing:"border-box",width:n,"-ms-transform":"translate(-50%,-38.1966%)","-moz-transform":"translate(-50%,-38.1966%)","-webkit-transform":"translate(-50%,-38.1966%)","-o-transform":"translate(-50%,-38.1966%)",transform:"translate(-50%,-38.1966%)"}).stylize(e("modalStyles",{overflow:"auto",backgroundColor:"white",padding:"20px",borderRadius:"5px"})).html(e("content")).attr("id",i).attr("role","dialog").attr("aria-labelledby",e("ariaLabelledBy")).attr("aria-describedby",e("ariaDescribedBy",i)).onClick(function(e){new k(e.target).anyAncestor(function(e){return/\bpico-close\b/.test(e.elem.className)})&&t()})}(a,x),c={modal:n,overlay:(o=x,k.make((i=a)("parent")).clazz("pico-overlay").clazz(i("overlayClass","")).stylize({display:"none",position:"fixed",top:"0px",left:"0px",height:"100%",width:"100%",zIndex:1e4}).stylize(i("overlayStyles",{opacity:.5,background:"#000"})).onClick(function(){i("overlayClose",!0)&&o()})),close:function(e,t){if(t("closeButton",!0))return e.child("button").html(t("closeHtml","&#xD7;")).clazz("pico-close").clazz(t("closeClass","")).stylize(t("closeStyles",{borderRadius:"2px",border:0,padding:0,cursor:"pointer",height:"15px",width:"15px",position:"absolute",top:"5px",right:"5px",fontSize:"16px",textAlign:"center",lineHeight:"15px",background:"#CCC"})).attr("aria-label",t("close-label","Close"))}(n,a)},r.trigger(l,t)),c[e]}function E(e,t){return(e.msMatchesSelector||e.webkitMatchesSelector||e.matches).call(e,t)}function w(e){return!(N(e)||E(e,":disabled")||e.hasAttribute("contenteditable"))&&(e.hasAttribute("tabindex")||E(e,"input,select,textarea,button,a[href],area[href],iframe"))}function T(e){for(var t=e.getElementsByTagName("*"),n=0;n<t.length;n++)if(w(t[n]))return t[n]}return l={modalElem:I(v),closeElem:I(y),overlayElem:I(g),buildDom:C(_.bind(null,null)),isVisible:function(){return!!(c&&v&&v().isVisible())},show:function(e){return t.trigger(l,e)&&(g().show(),y(),v().show(),n.trigger(l,e)),this},close:C(x),forceClose:C(b),destroy:function(){v().destroy(),g().destroy(),g=v=y=void 0},options:function(t){Object.keys(t).map(function(e){i[e]=t[e]})},afterCreate:C(r.watch),beforeShow:C(t.watch),afterShow:C(n.watch),beforeClose:C(o.watch),afterClose:C(s.watch)},d=l,h=a.bind(null,"focus",!0),d.beforeShow(function(){e=document.activeElement}),d.afterShow(function(){var e;!h()||(e=T(d.modalElem()))&&e.focus()}),d.afterClose(function(){h()&&e&&e.focus(),e=null}),A.watch(function(e){var t,n;h()&&d.isVisible()&&(t=T(d.modalElem()),n=function(e){for(var t=e.getElementsByTagName("*"),n=t.length;n--;)if(w(t[n]))return t[n]}(d.modalElem()),(e.shiftKey?t:n)===document.activeElement&&((e.shiftKey?n:t).focus(),e.preventDefault()))}),u=l,p=a.bind(null,"bodyOverflow",!0),m=new k(document.body),u.beforeShow(function(){f=m.elem.style.overflow,p()&&m.stylize({overflow:"hidden"})}),u.afterClose(function(){m.stylize({overflow:f})}),D.watch(function(){a("escCloses",!0)&&l.isVisible()&&l.close()}),l}})?i.apply(t,o):i)||(e.exports=r)}()},function(e,t,n){"use strict";function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}n.d(t,"a",function(){return o});var o=function(){function r(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),this.errorTableVisible=e.errorTableVisible,this.onToggleVisibility=e.onToggleVisibility,this.onFocusLine=e.onFocusLine||function(){},this.onChangeHeight=e.onChangeHeight,this.dom={};var t=document.createElement("div");t.className="jsoneditor-validation-errors-container",this.dom.validationErrorsContainer=t;var n=document.createElement("div");n.style.display="none",n.className="jsoneditor-additional-errors fadein",n.textContent="Scroll for more ▿",this.dom.additionalErrorsIndication=n,t.appendChild(n);var i=document.createElement("span");i.className="jsoneditor-validation-error-icon",i.style.display="none",this.dom.validationErrorIcon=i;var o=document.createElement("span");o.className="jsoneditor-validation-error-count",o.style.display="none",this.dom.validationErrorCount=o,this.dom.parseErrorIndication=document.createElement("span"),this.dom.parseErrorIndication.className="jsoneditor-parse-error-icon",this.dom.parseErrorIndication.style.display="none"}var e,t,n;return e=r,(t=[{key:"getErrorTable",value:function(){return this.dom.validationErrorsContainer}},{key:"getErrorCounter",value:function(){return this.dom.validationErrorCount}},{key:"getWarningIcon",value:function(){return this.dom.validationErrorIcon}},{key:"getErrorIcon",value:function(){return this.dom.parseErrorIndication}},{key:"toggleTableVisibility",value:function(){this.errorTableVisible=!this.errorTableVisible,this.onToggleVisibility(this.errorTableVisible)}},{key:"setErrors",value:function(e,u){var t,n,p,i,f=this;this.dom.validationErrors&&(this.dom.validationErrors.parentNode.removeChild(this.dom.validationErrors),this.dom.validationErrors=null,this.dom.additionalErrorsIndication.style.display="none"),this.errorTableVisible&&0<e.length?((t=document.createElement("div")).className="jsoneditor-validation-errors",(n=document.createElement("table")).className="jsoneditor-text-errors",t.appendChild(n),p=document.createElement("tbody"),n.appendChild(p),e.forEach(function(t){var e,n;isNaN(t.line)?!t.dataPath||(n=u.find(function(e){return e.path===t.dataPath}))&&(e=n.line+1):e=t.line;var i=document.createElement("tr");i.className=isNaN(e)?"":"jump-to-line","error"===t.type?i.className+=" parse-error":i.className+=" validation-error";var o=document.createElement("td"),r=document.createElement("button");r.className="jsoneditor-schema-error",o.appendChild(r),i.appendChild(o);var s,a,l,c,d,h=document.createElement("td");h.style="white-space: nowrap;",h.textContent=isNaN(e)?"":"Ln "+e,i.appendChild(h),"string"==typeof t?((s=document.createElement("td")).colSpan=2,(a=document.createElement("pre")).appendChild(document.createTextNode(t)),s.appendChild(a),i.appendChild(s)):((l=document.createElement("td")).appendChild(document.createTextNode(t.dataPath||"")),i.appendChild(l),c=document.createElement("td"),(d=document.createElement("pre")).appendChild(document.createTextNode(t.message)),c.appendChild(d),i.appendChild(c)),i.onclick=function(){f.onFocusLine(e)},p.appendChild(i)}),this.dom.validationErrors=t,this.dom.validationErrorsContainer.appendChild(t),this.dom.additionalErrorsIndication.title=e.length+" errors total",this.dom.validationErrorsContainer.clientHeight<this.dom.validationErrorsContainer.scrollHeight?(this.dom.additionalErrorsIndication.style.display="block",this.dom.validationErrorsContainer.onscroll=function(){f.dom.additionalErrorsIndication.style.display=0<f.dom.validationErrorsContainer.clientHeight&&0===f.dom.validationErrorsContainer.scrollTop?"block":"none"}):this.dom.validationErrorsContainer.onscroll=void 0,i=this.dom.validationErrorsContainer.clientHeight+(this.dom.statusBar?this.dom.statusBar.clientHeight:0),this.onChangeHeight(i)):this.onChangeHeight(0);var o,r=e.filter(function(e){return"error"!==e.type}).length;0<r?(this.dom.validationErrorCount.style.display="inline",this.dom.validationErrorCount.innerText=r,this.dom.validationErrorCount.onclick=this.toggleTableVisibility.bind(this),this.dom.validationErrorIcon.style.display="inline",this.dom.validationErrorIcon.title=r+" schema validation error(s) found",this.dom.validationErrorIcon.onclick=this.toggleTableVisibility.bind(this)):(this.dom.validationErrorCount.style.display="none",this.dom.validationErrorIcon.style.display="none"),e.some(function(e){return"error"===e.type})?(o=e[0].line,this.dom.parseErrorIndication.style.display="block",this.dom.parseErrorIndication.title=isNaN(o)?"parse error - check that the json is valid":"parse error on line "+o,this.dom.parseErrorIndication.onclick=this.toggleTableVisibility.bind(this)):this.dom.parseErrorIndication.style.display="none"}}])&&i(e.prototype,t),n&&i(e,n),r}()},function(e,t,n){var i;if(window.ace)i=window.ace;else try{i=n(!function(){var e=new Error("Cannot find module 'ace-builds/src-noconflict/ace'");throw e.code="MODULE_NOT_FOUND",e}()),n(!function(){var e=new Error("Cannot find module 'ace-builds/src-noconflict/mode-json'");throw e.code="MODULE_NOT_FOUND",e}()),n(!function(){var e=new Error("Cannot find module 'ace-builds/src-noconflict/ext-searchbox'");throw e.code="MODULE_NOT_FOUND",e}());var o=n(!function(){var e=new Error("Cannot find module '../generated/worker-json-data-url'");throw e.code="MODULE_NOT_FOUND",e}());i.config.setModuleUrl("ace/mode/json_worker",o)}catch(e){}e.exports=i},function(e,t,n){"use strict";n.r(t),n.d(t,"textModeMixins",function(){return c});var i=n(15),j=n.n(i),S=n(1),N=n(7),k=n(14),O=n(0);var r=n(5),a=n(6),I=n(8),l=n(2),D=n(21),A=n(4);function P(e){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o={},F="ace/theme/jsoneditor";function s(){try{this.format()}catch(e){}}o.create=function(e){var t=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};void 0===n.statusBar&&(n.statusBar=!0),n.mainMenuBar=!1!==n.mainMenuBar,n.enableSort=!1!==n.enableSort,n.enableTransform=!1!==n.enableTransform,n.createQuery=n.createQuery||A.a,n.executeQuery=n.executeQuery||A.b,"number"==typeof(this.options=n).indentation?this.indentation=Number(n.indentation):this.indentation=2,Object(S.b)(this.options.languages),Object(S.a)(this.options.language);var i=n.ace?n.ace:j.a;this.mode="code"===n.mode?"code":"text","code"===this.mode&&void 0===i&&(this.mode="text",console.warn("Failed to load Ace editor, falling back to plain text mode. Please use a JSONEditor bundle including Ace, or pass Ace as via the configuration option `ace`.")),this.theme=n.theme||F,this.theme===F&&i&&Object(D.tryRequireThemeJsonEditor)(),n.onTextSelectionChange&&this.onTextSelectionChange(n.onTextSelectionChange);var o=this;this.container=e,this.dom={},this.aceEditor=void 0,this.textarea=void 0,this.validateSchema=null,this.annotations=[],this.lastSchemaErrors=void 0,this._debouncedValidate=Object(O.debounce)(this.validate.bind(this),this.DEBOUNCE_INTERVAL),this.width=e.clientWidth,this.height=e.clientHeight,this.frame=document.createElement("div"),this.frame.className="jsoneditor jsoneditor-mode-"+this.options.mode,this.frame.onclick=function(e){e.preventDefault()},this.frame.onkeydown=function(e){o._onKeyDown(e)};var r,s,a,l,c,d,h,u,p={target:this.frame,onFocus:this.options.onFocus||null,onBlur:this.options.onBlur||null};this.frameFocusTracker=new I.a(p),this.content=document.createElement("div"),this.content.className="jsoneditor-outer",this.options.mainMenuBar&&(Object(O.addClassName)(this.content,"has-main-menu-bar"),this.menu=document.createElement("div"),this.menu.className="jsoneditor-menu",this.frame.appendChild(this.menu),(r=document.createElement("button")).type="button",r.className="jsoneditor-format",r.title=Object(S.c)("formatTitle"),this.menu.appendChild(r),r.onclick=function(){try{o.format(),o._onChange()}catch(e){o._onError(e)}},(s=document.createElement("button")).type="button",s.className="jsoneditor-compact",s.title=Object(S.c)("compactTitle"),this.menu.appendChild(s),s.onclick=function(){try{o.compact(),o._onChange()}catch(e){o._onError(e)}},this.options.enableSort&&((a=document.createElement("button")).type="button",a.className="jsoneditor-sort",a.title=Object(S.c)("sortTitleShort"),a.onclick=function(){o._showSortModal()},this.menu.appendChild(a)),this.options.enableTransform&&((l=document.createElement("button")).type="button",l.title=Object(S.c)("transformTitleShort"),l.className="jsoneditor-transform",l.onclick=function(){o._showTransformModal()},this.menu.appendChild(l)),(c=document.createElement("button")).type="button",c.className="jsoneditor-repair",c.title=Object(S.c)("repairTitle"),this.menu.appendChild(c),c.onclick=function(){try{o.repair(),o._onChange()}catch(e){o._onError(e)}},"code"===this.mode&&((d=document.createElement("button")).type="button",d.className="jsoneditor-undo jsoneditor-separator",d.title=Object(S.c)("undo"),d.onclick=function(){t.aceEditor.getSession().getUndoManager().undo()},this.menu.appendChild(d),this.dom.undo=d,(h=document.createElement("button")).type="button",h.className="jsoneditor-redo",h.title=Object(S.c)("redo"),h.onclick=function(){t.aceEditor.getSession().getUndoManager().redo()},this.menu.appendChild(h),this.dom.redo=h),this.options&&this.options.modes&&this.options.modes.length&&(this.modeSwitcher=new N.a(this.menu,this.options.modes,this.options.mode,function(e){o.setMode(e),o.modeSwitcher.focus()})),"code"===this.mode&&((u=document.createElement("a")).appendChild(document.createTextNode("powered by ace")),u.href="https://ace.c9.io/",u.target="_blank",u.className="jsoneditor-poweredBy",u.onclick=function(){window.open(u.href,u.target)},this.menu.appendChild(u)));var f,m,v,g,y,b,x,C,_,E,w,T=this.options.onEditable&&P("function"===this.options.onEditable)&&!this.options.onEditable({});this.frame.appendChild(this.content),this.container.appendChild(this.frame),"code"===this.mode?(this.editorDom=document.createElement("div"),this.editorDom.style.height="100%",this.editorDom.style.width="100%",this.content.appendChild(this.editorDom),m=(f=i.edit(this.editorDom)).getSession(),f.$blockScrolling=1/0,f.setTheme(this.theme),f.setOptions({readOnly:T}),f.setShowPrintMargin(!1),f.setFontSize("13px"),m.setMode("ace/mode/json"),m.setTabSize(this.indentation),m.setUseSoftTabs(!0),m.setUseWrapMode(!0),v=m.setAnnotations,m.setAnnotations=function(e){v.call(this,e&&e.length?e:o.annotations)},f.commands.bindKey("Ctrl-L",null),f.commands.bindKey("Command-L",null),(this.aceEditor=f).on("change",this._onChange.bind(this)),f.on("changeSelection",this._onSelect.bind(this))):((g=document.createElement("textarea")).className="jsoneditor-text",g.spellcheck=!1,this.content.appendChild(g),this.textarea=g,this.textarea.readOnly=T,null===this.textarea.oninput?this.textarea.oninput=this._onChange.bind(this):this.textarea.onchange=this._onChange.bind(this),g.onselect=this._onSelect.bind(this),g.onmousedown=this._onMouseDown.bind(this),g.onblur=this._onBlur.bind(this)),this._updateHistoryButtons(),this.errorTable=new k.a({errorTableVisible:"text"===this.mode,onToggleVisibility:function(){o.validate()},onFocusLine:function(e){o.isFocused=!0,isNaN(e)||o.setTextSelection({row:e,column:1},{row:e,column:1e3})},onChangeHeight:function(e){var t=e+(o.dom.statusBar?o.dom.statusBar.clientHeight:0)+1;o.content.style.marginBottom=-t+"px",o.content.style.paddingBottom=t+"px"}}),this.frame.appendChild(this.errorTable.getErrorTable()),n.statusBar&&(Object(O.addClassName)(this.content,"has-status-bar"),this.curserInfoElements={},y=document.createElement("div"),(this.dom.statusBar=y).className="jsoneditor-statusbar",this.frame.appendChild(y),(b=document.createElement("span")).className="jsoneditor-curserinfo-label",b.innerText="Ln:",(x=document.createElement("span")).className="jsoneditor-curserinfo-val",x.innerText="1",y.appendChild(b),y.appendChild(x),(C=document.createElement("span")).className="jsoneditor-curserinfo-label",C.innerText="Col:",(_=document.createElement("span")).className="jsoneditor-curserinfo-val",_.innerText="1",y.appendChild(C),y.appendChild(_),this.curserInfoElements.colVal=_,this.curserInfoElements.lnVal=x,(E=document.createElement("span")).className="jsoneditor-curserinfo-label",E.innerText="characters selected",E.style.display="none",(w=document.createElement("span")).className="jsoneditor-curserinfo-count",w.innerText="0",w.style.display="none",this.curserInfoElements.countLabel=E,this.curserInfoElements.countVal=w,y.appendChild(w),y.appendChild(E),y.appendChild(this.errorTable.getErrorCounter()),y.appendChild(this.errorTable.getWarningIcon()),y.appendChild(this.errorTable.getErrorIcon())),this.setSchema(this.options.schema,this.options.schemaRefs)},o._onChange=function(){var e=this;if(!this.onChangeDisabled){if(setTimeout(function(){return e._updateHistoryButtons()}),this._debouncedValidate(),this.options.onChange)try{this.options.onChange()}catch(e){console.error("Error in onChange callback: ",e)}if(this.options.onChangeText)try{this.options.onChangeText(this.getText())}catch(e){console.error("Error in onChangeText callback: ",e)}}},o._updateHistoryButtons=function(){var e;this.aceEditor&&this.dom.undo&&this.dom.redo&&((e=this.aceEditor.getSession().getUndoManager())&&e.hasUndo&&e.hasRedo&&(this.dom.undo.disabled=!e.hasUndo(),this.dom.redo.disabled=!e.hasRedo()))},o._showSortModal=function(){var i=this,e=this.options.modalAnchor||l.a,o=this.get();Object(r.showSortModal)(e,o,function(e){var t,n;Array.isArray(o)&&(t=Object(O.sort)(o,e.path,e.direction),i.sortedBy=e,i.update(t)),Object(O.isObject)(o)&&(n=Object(O.sortObjectKeys)(o,e.direction),i.sortedBy=e,i.update(n))},i.sortedBy)},o._showTransformModal=function(){var n=this,e=this.options,t=e.modalAnchor,i=e.createQuery,o=e.executeQuery,r=e.queryDescription,s=this.get();Object(a.showTransformModal)({anchor:t||l.a,json:s,queryDescription:r,createQuery:i,executeQuery:o,onTransform:function(e){var t=o(s,e);n.update(t)}})},o._onSelect=function(){this._updateCursorInfo(),this._emitSelectionChange()},o._onKeyDown=function(e){var t=!1;220===(e.which||e.keyCode)&&e.ctrlKey&&(e.shiftKey?this.compact():this.format(),this._onChange(),t=!0),t&&(e.preventDefault(),e.stopPropagation()),this._updateCursorInfo(),this._emitSelectionChange()},o._onMouseDown=function(){this._updateCursorInfo(),this._emitSelectionChange()},o._onBlur=function(){var e=this;setTimeout(function(){e.isFocused||(e._updateCursorInfo(),e._emitSelectionChange()),e.isFocused=!1})},o._updateCursorInfo=function(){var e,t,n,i,o,r=this;function s(){r.curserInfoElements.countVal.innerText!==o&&(r.curserInfoElements.countVal.innerText=o,r.curserInfoElements.countVal.style.display=o?"inline":"none",r.curserInfoElements.countLabel.style.display=o?"inline":"none"),r.curserInfoElements.lnVal.innerText=n,r.curserInfoElements.colVal.innerText=i}this.textarea?setTimeout(function(){var e=Object(O.getInputSelection)(r.textarea);e.startIndex!==e.endIndex&&(o=e.endIndex-e.startIndex),i=o&&r.cursorInfo&&r.cursorInfo.line===e.end.row&&r.cursorInfo.column===e.end.column?(n=e.start.row,e.start.column):(n=e.end.row,e.end.column),r.cursorInfo={line:n,column:i,count:o},r.options.statusBar&&s()},0):this.aceEditor&&this.curserInfoElements&&(e=this.aceEditor.getCursorPosition(),t=this.aceEditor.getSelectedText(),n=e.row+1,i=e.column+1,o=t.length,r.cursorInfo={line:n,column:i,count:o},this.options.statusBar&&s())},o._emitSelectionChange=function(){var e;this._selectionChangedHandler&&(e=this.getTextSelection(),this._selectionChangedHandler(e.start,e.end,e.text))},o._refreshAnnotations=function(){var e,t=this.aceEditor&&this.aceEditor.getSession();t&&(e=t.getAnnotations().filter(function(e){return"error"===e.type}),t.setAnnotations(e))},o.destroy=function(){this.aceEditor&&(this.aceEditor.destroy(),this.aceEditor=null),this.frame&&this.container&&this.frame.parentNode===this.container&&this.container.removeChild(this.frame),this.modeSwitcher&&(this.modeSwitcher.destroy(),this.modeSwitcher=null),this.textarea=null,this._debouncedValidate=null,this.frameFocusTracker.destroy()},o.compact=function(){var e=this.get(),t=JSON.stringify(e);this.updateText(t)},o.format=function(){var e=this.get(),t=JSON.stringify(e,null,this.indentation);this.updateText(t)},o.repair=function(){var e=this.getText(),t=Object(O.repair)(e);this.updateText(t)},o.focus=function(){this.textarea&&this.textarea.focus(),this.aceEditor&&this.aceEditor.focus()},o.resize=function(){this.aceEditor&&this.aceEditor.resize(!1)},o.set=function(e){this.setText(JSON.stringify(e,null,this.indentation))},o.update=function(e){this.updateText(JSON.stringify(e,null,this.indentation))},o.get=function(){var e=this.getText();return Object(O.parse)(e)},o.getText=function(){return this.textarea?this.textarea.value:this.aceEditor?this.aceEditor.getValue():""},o._setText=function(e,t){var n,i=this,o=!0===this.options.escapeUnicode?Object(O.escapeUnicodeChars)(e):e;this.textarea&&(this.textarea.value=o),this.aceEditor&&(this.onChangeDisabled=!0,this.aceEditor.setValue(o,-1),this.onChangeDisabled=!1,t&&(n=this,setTimeout(function(){n.aceEditor&&n.aceEditor.session.getUndoManager().reset()})),setTimeout(function(){return i._updateHistoryButtons()})),this._debouncedValidate()},o.setText=function(e){this._setText(e,!0)},o.updateText=function(e){this.getText()!==e&&this._setText(e,!1)},o.validate=function(){var t,n,i=this,o=[],r=[];try{var e=this.get();this.validateSchema&&(this.validateSchema(e)||(o=this.validateSchema.errors.map(function(e){return e.type="validation",Object(O.improveSchemaError)(e)}))),this.validationSequence=(this.validationSequence||0)+1;var s=this,a=this.validationSequence;(function(e,t){if(!t)return Promise.resolve([]);try{var n=t(e);return(Object(O.isPromise)(n)?n:Promise.resolve(n)).then(function(e){return Array.isArray(e)?e.filter(function(e){var t=Object(O.isValidValidationError)(e);return t||console.warn('Ignoring a custom validation error with invalid structure. Expected structure: {path: [...], message: "..."}. Actual error:',e),t}).map(function(e){return{dataPath:Object(O.stringifyPath)(e.path),message:e.message,type:"customValidation"}}):[]})}catch(e){return Promise.reject(e)}})(e,this.options.onValidate).then(function(e){var t;a===s.validationSequence&&(t=o.concat(r).concat(e),s._renderErrors(t),"function"==typeof i.options.onValidationError&&(Object(O.isValidationErrorChanged)(t,i.lastSchemaErrors)&&i.options.onValidationError.call(i,t),i.lastSchemaErrors=t))}).catch(function(e){console.error("Custom validation function did throw an error",e)})}catch(e){this.getText()&&((t=/\w*line\s*(\d+)\w*/g.exec(e.message))&&(n=+t[1]),r=[{type:"error",message:e.message.replace(/\n/g,"<br>"),line:n}]),this._renderErrors(r),"function"==typeof this.options.onValidationError&&(Object(O.isValidationErrorChanged)(r,this.lastSchemaErrors)&&this.options.onValidationError.call(this,r),this.lastSchemaErrors=r)}},o._renderErrors=function(i){var e=this.getText(),t=[];i.reduce(function(e,t){return"string"==typeof t.dataPath&&-1===e.indexOf(t.dataPath)&&e.push(t.dataPath),e},t);var n=Object(O.getPositionForPath)(e,t);this.aceEditor&&(this.annotations=n.map(function(t){var e=i.filter(function(e){return e.dataPath===t.path}),n=e.map(function(e){return e.message}).join("\n");return n?{row:t.line,column:t.column,text:"Schema validation error"+(1!==e.length?"s":"")+": \n"+n,type:"warning",source:"jsoneditor"}:{}}),this._refreshAnnotations()),this.errorTable.setErrors(i,n),this.aceEditor&&this.aceEditor.resize(!1)},o.getTextSelection=function(){var e={};if(this.textarea){var t=Object(O.getInputSelection)(this.textarea);return this.cursorInfo&&this.cursorInfo.line===t.end.row&&this.cursorInfo.column===t.end.column?(e.start=t.end,e.end=t.start):e=t,{start:e.start,end:e.end,text:this.textarea.value.substring(t.startIndex,t.endIndex)}}if(this.aceEditor){var n=this.aceEditor.getSelection(),i=this.aceEditor.getSelectedText(),o=n.getRange(),r=n.getSelectionLead();return r.row===o.end.row&&r.column===o.end.column?e=o:(e.start=o.end,e.end=o.start),{start:{row:e.start.row+1,column:e.start.column+1},end:{row:e.end.row+1,column:e.end.column+1},text:i}}},o.onTextSelectionChange=function(e){"function"==typeof e&&(this._selectionChangedHandler=Object(O.debounce)(e,this.DEBOUNCE_INTERVAL))},o.setTextSelection=function(e,t){var n,i,o,r,s,a,l;e&&t&&(this.textarea?(n=Object(O.getIndexForPosition)(this.textarea,e.row,e.column),i=Object(O.getIndexForPosition)(this.textarea,t.row,t.column),-1<n&&-1<i&&(this.textarea.setSelectionRange?(this.textarea.focus(),this.textarea.setSelectionRange(n,i)):this.textarea.createTextRange&&((o=this.textarea.createTextRange()).collapse(!0),o.moveEnd("character",i),o.moveStart("character",n),o.select()),r=(this.textarea.value.match(/\n/g)||[]).length+1,s=this.textarea.scrollHeight/r,a=e.row*s,this.textarea.scrollTop=a>this.textarea.clientHeight?a-this.textarea.clientHeight/2:0)):this.aceEditor&&(l={start:{row:e.row-1,column:e.column-1},end:{row:t.row-1,column:t.column-1}},this.aceEditor.selection.setRange(l),this.aceEditor.scrollToLine(e.row-1,!0)))};var c=[{mode:"text",mixin:o,data:"text",load:s},{mode:"code",mixin:o,data:"text",load:s}]},function(e,t,n){var i,o=((i={trace:function(){},yy:{},symbols_:{error:2,JSONString:3,STRING:4,JSONNumber:5,NUMBER:6,JSONNullLiteral:7,NULL:8,JSONBooleanLiteral:9,TRUE:10,FALSE:11,JSONText:12,JSONValue:13,EOF:14,JSONObject:15,JSONArray:16,"{":17,"}":18,JSONMemberList:19,JSONMember:20,":":21,",":22,"[":23,"]":24,JSONElementList:25,$accept:0,$end:1},terminals_:{2:"error",4:"STRING",6:"NUMBER",8:"NULL",10:"TRUE",11:"FALSE",14:"EOF",17:"{",18:"}",21:":",22:",",23:"[",24:"]"},productions_:[0,[3,1],[5,1],[7,1],[9,1],[9,1],[12,2],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[15,2],[15,3],[20,3],[19,1],[19,3],[16,2],[16,3],[25,1],[25,3]],performAction:function(e,t,n,i,o,r){var s=r.length-1;switch(o){case 1:this.$=e.replace(/\\(\\|")/g,"$1").replace(/\\n/g,"\n").replace(/\\r/g,"\r").replace(/\\t/g,"\t").replace(/\\v/g,"\v").replace(/\\f/g,"\f").replace(/\\b/g,"\b");break;case 2:this.$=Number(e);break;case 3:this.$=null;break;case 4:this.$=!0;break;case 5:this.$=!1;break;case 6:return this.$=r[s-1];case 13:this.$={};break;case 14:this.$=r[s-1];break;case 15:this.$=[r[s-2],r[s]];break;case 16:this.$={},this.$[r[s][0]]=r[s][1];break;case 17:this.$=r[s-2],r[s-2][r[s][0]]=r[s][1];break;case 18:this.$=[];break;case 19:this.$=r[s-1];break;case 20:this.$=[r[s]];break;case 21:this.$=r[s-2],r[s-2].push(r[s])}},table:[{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],12:1,13:2,15:7,16:8,17:[1,14],23:[1,15]},{1:[3]},{14:[1,16]},{14:[2,7],18:[2,7],22:[2,7],24:[2,7]},{14:[2,8],18:[2,8],22:[2,8],24:[2,8]},{14:[2,9],18:[2,9],22:[2,9],24:[2,9]},{14:[2,10],18:[2,10],22:[2,10],24:[2,10]},{14:[2,11],18:[2,11],22:[2,11],24:[2,11]},{14:[2,12],18:[2,12],22:[2,12],24:[2,12]},{14:[2,3],18:[2,3],22:[2,3],24:[2,3]},{14:[2,4],18:[2,4],22:[2,4],24:[2,4]},{14:[2,5],18:[2,5],22:[2,5],24:[2,5]},{14:[2,1],18:[2,1],21:[2,1],22:[2,1],24:[2,1]},{14:[2,2],18:[2,2],22:[2,2],24:[2,2]},{3:20,4:[1,12],18:[1,17],19:18,20:19},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:23,15:7,16:8,17:[1,14],23:[1,15],24:[1,21],25:22},{1:[2,6]},{14:[2,13],18:[2,13],22:[2,13],24:[2,13]},{18:[1,24],22:[1,25]},{18:[2,16],22:[2,16]},{21:[1,26]},{14:[2,18],18:[2,18],22:[2,18],24:[2,18]},{22:[1,28],24:[1,27]},{22:[2,20],24:[2,20]},{14:[2,14],18:[2,14],22:[2,14],24:[2,14]},{3:20,4:[1,12],20:29},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:30,15:7,16:8,17:[1,14],23:[1,15]},{14:[2,19],18:[2,19],22:[2,19],24:[2,19]},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:31,15:7,16:8,17:[1,14],23:[1,15]},{18:[2,17],22:[2,17]},{18:[2,15],22:[2,15]},{22:[2,21],24:[2,21]}],defaultActions:{16:[2,6]},parseError:function(e){throw new Error(e)},parse:function(e){var t=this,n=[0],i=[null],o=[],r=this.table,s="",a=0,l=0,c=0;this.lexer.setInput(e),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,void 0===this.lexer.yylloc&&(this.lexer.yylloc={});var d=this.lexer.yylloc;function h(){var e=t.lexer.lex()||1;return"number"!=typeof e&&(e=t.symbols_[e]||e),e}o.push(d),"function"==typeof this.yy.parseError&&(this.parseError=this.yy.parseError);for(var u,p,f,m,v,g,y,b,x,C,_={};;){if(f=n[n.length-1],void 0===(m=this.defaultActions[f]?this.defaultActions[f]:(null==u&&(u=h()),r[f]&&r[f][u]))||!m.length||!m[0]){if(!c){for(g in x=[],r[f])this.terminals_[g]&&2<g&&x.push("'"+this.terminals_[g]+"'");var E="",E=this.lexer.showPosition?"Parse error on line "+(a+1)+":\n"+this.lexer.showPosition()+"\nExpecting "+x.join(", ")+", got '"+this.terminals_[u]+"'":"Parse error on line "+(a+1)+": Unexpected "+(1==u?"end of input":"'"+(this.terminals_[u]||u)+"'");this.parseError(E,{text:this.lexer.match,token:this.terminals_[u]||u,line:this.lexer.yylineno,loc:d,expected:x})}if(3==c){if(1==u)throw new Error(E||"Parsing halted.");l=this.lexer.yyleng,s=this.lexer.yytext,a=this.lexer.yylineno,d=this.lexer.yylloc,u=h()}for(;!(2..toString()in r[f]);){if(0==f)throw new Error(E||"Parsing halted.");C=1,n.length=n.length-2*C,i.length=i.length-C,o.length=o.length-C,f=n[n.length-1]}p=u,u=2,m=r[f=n[n.length-1]]&&r[f][2],c=3}if(m[0]instanceof Array&&1<m.length)throw new Error("Parse Error: multiple actions possible at state: "+f+", token: "+u);switch(m[0]){case 1:n.push(u),i.push(this.lexer.yytext),o.push(this.lexer.yylloc),n.push(m[1]),u=null,p?(u=p,p=null):(l=this.lexer.yyleng,s=this.lexer.yytext,a=this.lexer.yylineno,d=this.lexer.yylloc,0<c&&c--);break;case 2:if(y=this.productions_[m[1]][1],_.$=i[i.length-y],_._$={first_line:o[o.length-(y||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(y||1)].first_column,last_column:o[o.length-1].last_column},void 0!==(v=this.performAction.call(_,s,l,a,this.yy,m[1],i,o)))return v;y&&(n=n.slice(0,-1*y*2),i=i.slice(0,-1*y),o=o.slice(0,-1*y)),n.push(this.productions_[m[1]][0]),i.push(_.$),o.push(_._$),b=r[n[n.length-2]][n[n.length-1]],n.push(b);break;case 3:return!0}}return!0}}).lexer={EOF:1,parseError:function(e,t){if(!this.yy.parseError)throw new Error(e);this.yy.parseError(e,t)},setInput:function(e){return this._input=e,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this},input:function(){var e=this._input[0];return this.yytext+=e,this.yyleng++,this.match+=e,this.matched+=e,e.match(/\n/)&&this.yylineno++,this._input=this._input.slice(1),e},unput:function(e){return this._input=e+this._input,this},more:function(){return this._more=!0,this},less:function(e){this._input=this.match.slice(e)+this._input},pastInput:function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(20<e.length?"...":"")+e.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var e=this.match;return e.length<20&&(e+=this._input.substr(0,20-e.length)),(e.substr(0,20)+(20<e.length?"...":"")).replace(/\n/g,"")},showPosition:function(){var e=this.pastInput(),t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"},next:function(){if(this.done)return this.EOF;var e,t,n,i,o;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var r=this._currentRules(),s=0;s<r.length&&(!(n=this._input.match(this.rules[r[s]]))||t&&!(n[0].length>t[0].length)||(t=n,i=s,this.options.flex));s++);return t?((o=t[0].match(/\n.*/g))&&(this.yylineno+=o.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:o?o[o.length-1].length-1:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.yyleng=this.yytext.length,this._more=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],e=this.performAction.call(this,this.yy,this,r[i],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),e||void 0):""===this._input?this.EOF:void this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var e=this.next();return void 0!==e?e:this.lex()},begin:function(e){this.conditionStack.push(e)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(e){this.begin(e)},options:{},performAction:function(e,t,n){switch(n){case 0:break;case 1:return 6;case 2:return t.yytext=t.yytext.substr(1,t.yyleng-2),4;case 3:return 17;case 4:return 18;case 5:return 23;case 6:return 24;case 7:return 22;case 8:return 21;case 9:return 10;case 10:return 11;case 11:return 8;case 12:return 14;case 13:return"INVALID"}},rules:[/^(?:\s+)/,/^(?:(-?([0-9]|[1-9][0-9]+))(\.[0-9]+)?([eE][-+]?[0-9]+)?\b)/,/^(?:"(?:\\[\\"bfnrt/]|\\u[a-fA-F0-9]{4}|[^\\\0-\x09\x0a-\x1f"])*")/,/^(?:\{)/,/^(?:\})/,/^(?:\[)/,/^(?:\])/,/^(?:,)/,/^(?::)/,/^(?:true\b)/,/^(?:false\b)/,/^(?:null\b)/,/^(?:$)/,/^(?:.)/],conditions:{INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,10,11,12,13],inclusive:!0}}},i);t.parser=o,t.parse=o.parse.bind(o)},function(e,t){function n(e){"remove"in e||Object.defineProperty(e,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){void 0!==this.parentNode&&this.parentNode.removeChild(this)}})}"undefined"!=typeof Element&&(void 0!==window.Element&&n(window.Element.prototype),void 0!==window.CharacterData&&n(window.CharacterData.prototype),void 0!==window.DocumentType&&n(window.DocumentType.prototype)),Array.prototype.findIndex||Object.defineProperty(Array.prototype,"findIndex",{value:function(e){for(var t=0;t<this.length;t++){var n=this[t];if(e.call(this,n,t,this))return t}return-1},configurable:!0,writable:!0}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(e){var t=this.findIndex(e);return this[t]},configurable:!0,writable:!0}),String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")})},function(e,t,n){"use strict";var C={b:"\b",f:"\f",n:"\n",r:"\r",t:"\t",'"':'"',"/":"/","\\":"\\"},_="a".charCodeAt();t.parse=function(o,e,t){var i={},n=0,r=0,s=0,a=t&&t.bigint&&"undefined"!=typeof BigInt;return{data:l("",!0),pointers:i};function l(e,t){var n;c(),m(e,"value");var i=u();switch(i){case"t":h("rue"),n=!0;break;case"f":h("alse"),n=!1;break;case"n":h("ull"),n=null;break;case'"':n=d();break;case"[":n=function(e){c();var t=[],n=0;if("]"==u())return t;p();for(;;){var i=e+"/"+n;t.push(l(i)),c();var o=u();if("]"==o)break;","!=o&&b(),c(),n++}return t}(e);break;case"{":n=function(e){c();var t={};if("}"==u())return t;p();for(;;){var n=g();'"'!=u()&&b();var i=d(),o=e+"/"+E(i);v(o,"key",n),m(o,"keyEnd"),c(),":"!=u()&&b(),c(),t[i]=l(o),c();var r=u();if("}"==r)break;","!=r&&b(),c()}return t}(e);break;default:p(),0<="-0123456789".indexOf(i)?n=function(){var e="",t=!0;"-"==o[s]&&(e+=u());e+=("0"==o[s]?u:f)(),"."==o[s]&&(e+=u()+f(),t=!1);"e"!=o[s]&&"E"!=o[s]||(e+=u(),"+"!=o[s]&&"-"!=o[s]||(e+=u()),e+=f(),t=!1);var n=+e;return a&&t&&(n>Number.MAX_SAFE_INTEGER||n<Number.MIN_SAFE_INTEGER)?BigInt(e):n}():y()}return m(e,"valueEnd"),c(),t&&s<o.length&&y(),n}function c(){e:for(;s<o.length;){switch(o[s]){case" ":r++;break;case"\t":r+=4;break;case"\r":r=0;break;case"\n":r=0,n++;break;default:break e}s++}}function d(){for(var e,t="";'"'!=(e=u());)"\\"==e?(e=u())in C?t+=C[e]:"u"==e?t+=function(){var e=4,t=0;for(;e--;){t<<=4;var n=u().toLowerCase();"a"<=n&&n<="f"?t+=n.charCodeAt()-_+10:"0"<=n&&n<="9"?t+=+n:b()}return String.fromCharCode(t)}():b():t+=e;return t}function h(e){for(var t=0;t<e.length;t++)u()!==e[t]&&b()}function u(){x();var e=o[s];return s++,r++,e}function p(){s--,r--}function f(){for(var e="";"0"<=o[s]&&o[s]<="9";)e+=u();if(e.length)return e;x(),y()}function m(e,t){v(e,t,g())}function v(e,t,n){i[e]=i[e]||{},i[e][t]=n}function g(){return{line:n,column:r,pos:s}}function y(){throw new SyntaxError("Unexpected token "+o[s]+" in JSON at position "+s)}function b(){p(),y()}function x(){if(s>=o.length)throw new SyntaxError("Unexpected end of JSON input")}},t.stringify=function(e,t,n){if(b(e)){var i=0;switch(typeof(p="object"==typeof n?n.space:n)){case"number":var o=10<p?10:p<0?0:Math.floor(p),p=o&&y(o," "),r=o,s=o;break;case"string":p=p.slice(0,10);for(var a=s=r=0;a<p.length;a++){switch(p[a]){case" ":s++;break;case"\t":s+=4;break;case"\r":s=0;break;case"\n":s=0,i++;break;default:throw new Error("whitespace characters not allowed in JSON")}r++}break;default:p=void 0}var l="",c={},d=0,h=0,u=0,f=n&&n.es6&&"function"==typeof Map;return function c(d,h,u){g(u,"value");switch(typeof d){case"number":case"bigint":case"boolean":m(""+d);break;case"string":m(x(d));break;case"object":null===d?m("null"):"function"==typeof d.toJSON?m(x(d.toJSON())):Array.isArray(d)?e():f?d.constructor.BYTES_PER_ELEMENT?e():d instanceof Map?n():d instanceof Set?n(!0):t():t()}g(u,"valueEnd");function e(){if(d.length){m("[");for(var e=h+1,t=0;t<d.length;t++){t&&m(","),v(e);var n=b(d[t])?d[t]:null,i=u+"/"+t;c(n,e,i)}v(h),m("]")}else m("[]")}function t(){var e=Object.keys(d);if(e.length){m("{");for(var t=h+1,n=0;n<e.length;n++){var i,o=e[n],r=d[o];b(r)&&(n&&m(","),i=u+"/"+E(o),v(t),g(i,"key"),m(x(o)),g(i,"keyEnd"),m(":"),p&&m(" "),c(r,t,i))}v(h),m("}")}else m("{}")}function n(e){if(d.size){m("{");for(var t=h+1,n=!0,i=d.entries(),o=i.next();!o.done;){var r,s=o.value,a=s[0],l=!!e||s[1];b(l)&&(n||m(","),n=!1,r=u+"/"+E(a),v(t),g(r,"key"),m(x(a)),g(r,"keyEnd"),m(":"),p&&m(" "),c(l,t,r)),o=i.next()}v(h),m("}")}else m("{}")}}(e,0,""),{json:l,pointers:c}}function m(e){h+=e.length,u+=e.length,l+=e}function v(e){if(p){for(l+="\n"+y(e,p),d++,h=0;e--;)i?(d+=i,h=s):h+=s,u+=r;u+=1}}function g(e,t){c[e]=c[e]||{},c[e][t]={line:d,column:h,pos:u}}function y(e,t){return Array(e+1).join(t)}};var i=["number","bigint","boolean","string","object"];function b(e){return 0<=i.indexOf(typeof e)}var o=/"|\\/g,r=/[\b]/g,s=/\f/g,a=/\n/g,l=/\r/g,c=/\t/g;function x(e){return'"'+(e=e.replace(o,"\\$&").replace(s,"\\f").replace(r,"\\b").replace(a,"\\n").replace(l,"\\r").replace(c,"\\t"))+'"'}var d=/~/g,h=/\//g;function E(e){return e.replace(d,"~0").replace(h,"~1")}},function(e,t,n){!function(e){"use strict";function S(e){return null!==e&&"[object Array]"===Object.prototype.toString.call(e)}function N(e){return null!==e&&"[object Object]"===Object.prototype.toString.call(e)}function k(e,t){if(e===t)return!0;if(Object.prototype.toString.call(e)!==Object.prototype.toString.call(t))return!1;if(!0===S(e)){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!1===k(e[n],t[n]))return!1;return!0}if(!0!==N(e))return!1;var i={};for(var o in e)if(hasOwnProperty.call(e,o)){if(!1===k(e[o],t[o]))return!1;i[o]=!0}for(var r in t)if(hasOwnProperty.call(t,r)&&!0!==i[r])return!1;return!0}function O(e){if(""===e||!1===e||null===e)return!0;if(S(e)&&0===e.length)return!0;if(N(e)){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}return!1}var s="function"==typeof String.prototype.trimLeft?function(e){return e.trimLeft()}:function(e){return e.match(/^\s*(.*)/)[1]},c=0,r=1,d=2,a=3,t=4,n=6,l=8,h=9,u="UnquotedIdentifier",p="QuotedIdentifier",f="Rbracket",m="Rparen",v="Comma",g="Colon",y="Rbrace",b="Number",I="Current",D="Expref",A="Pipe",P="GTE",F="LTE",M="Flatten",x="Star",C="Filter",_="Dot",E="Lbrace",w="Lbracket",T="Lparen",j="Literal",V={".":_,"*":x,",":v,":":g,"{":E,"}":y,"]":f,"(":T,")":m,"@":I},B={"<":!0,">":!0,"=":!0,"!":!0},L={" ":!0,"\t":!0,"\n":!0};function R(e){return"0"<=e&&e<="9"||"-"===e}function i(){}i.prototype={tokenize:function(e){var t,n,i,o,r=[];for(this._current=0;this._current<e.length;)if("a"<=(o=e[this._current]
)&&o<="z"||"A"<=o&&o<="Z"||"_"===o)t=this._current,n=this._consumeUnquotedIdentifier(e),r.push({type:u,value:n,start:t});else if(void 0!==V[e[this._current]])r.push({type:V[e[this._current]],value:e[this._current],start:this._current}),this._current++;else if(R(e[this._current]))i=this._consumeNumber(e),r.push(i);else if("["===e[this._current])i=this._consumeLBracket(e),r.push(i);else if('"'===e[this._current])t=this._current,n=this._consumeQuotedIdentifier(e),r.push({type:p,value:n,start:t});else if("'"===e[this._current])t=this._current,n=this._consumeRawStringLiteral(e),r.push({type:j,value:n,start:t});else if("`"===e[this._current]){t=this._current;var s=this._consumeLiteral(e);r.push({type:j,value:s,start:t})}else if(void 0!==B[e[this._current]])r.push(this._consumeOperator(e));else if(void 0!==L[e[this._current]])this._current++;else if("&"===e[this._current])t=this._current,this._current++,"&"===e[this._current]?(this._current++,r.push({type:"And",value:"&&",start:t})):r.push({type:D,value:"&",start:t});else{if("|"!==e[this._current]){var a=new Error("Unknown character:"+e[this._current]);throw a.name="LexerError",a}t=this._current,this._current++,"|"===e[this._current]?(this._current++,r.push({type:"Or",value:"||",start:t})):r.push({type:A,value:"|",start:t})}return r},_consumeUnquotedIdentifier:function(e){var t,n=this._current;for(this._current++;this._current<e.length&&("a"<=(t=e[this._current])&&t<="z"||"A"<=t&&t<="Z"||"0"<=t&&t<="9"||"_"===t);)this._current++;return e.slice(n,this._current)},_consumeQuotedIdentifier:function(e){var t=this._current;this._current++;for(var n=e.length;'"'!==e[this._current]&&this._current<n;){var i=this._current;"\\"!==e[i]||"\\"!==e[i+1]&&'"'!==e[i+1]?i++:i+=2,this._current=i}return this._current++,JSON.parse(e.slice(t,this._current))},_consumeRawStringLiteral:function(e){var t=this._current;this._current++;for(var n=e.length;"'"!==e[this._current]&&this._current<n;){var i=this._current;"\\"!==e[i]||"\\"!==e[i+1]&&"'"!==e[i+1]?i++:i+=2,this._current=i}return this._current++,e.slice(t+1,this._current-1).replace("\\'","'")},_consumeNumber:function(e){var t=this._current;this._current++;for(var n=e.length;R(e[this._current])&&this._current<n;)this._current++;var i=parseInt(e.slice(t,this._current));return{type:b,value:i,start:t}},_consumeLBracket:function(e){var t=this._current;return this._current++,"?"===e[this._current]?(this._current++,{type:C,value:"[?",start:t}):"]"===e[this._current]?(this._current++,{type:M,value:"[]",start:t}):{type:w,value:"[",start:t}},_consumeOperator:function(e){var t=this._current,n=e[t];return this._current++,"!"===n?"="===e[this._current]?(this._current++,{type:"NE",value:"!=",start:t}):{type:"Not",value:"!",start:t}:"<"===n?"="===e[this._current]?(this._current++,{type:F,value:"<=",start:t}):{type:"LT",value:"<",start:t}:">"===n?"="===e[this._current]?(this._current++,{type:P,value:">=",start:t}):{type:"GT",value:">",start:t}:"="===n&&"="===e[this._current]?(this._current++,{type:"EQ",value:"==",start:t}):void 0},_consumeLiteral:function(e){this._current++;for(var t=this._current,n=e.length;"`"!==e[this._current]&&this._current<n;){var i=this._current;"\\"!==e[i]||"\\"!==e[i+1]&&"`"!==e[i+1]?i++:i+=2,this._current=i}var o=(o=s(e.slice(t,this._current))).replace("\\`","`"),r=this._looksLikeJSON(o)?JSON.parse(o):JSON.parse('"'+o+'"');return this._current++,r},_looksLikeJSON:function(e){if(""===e)return!1;if(0<='[{"'.indexOf(e[0]))return!0;if(0<=["true","false","null"].indexOf(e))return!0;if(!(0<="-0123456789".indexOf(e[0])))return!1;try{return JSON.parse(e),!0}catch(e){return!1}}};var z={};function H(){}function U(e){this.runtime=e}function J(e){this._interpreter=e,this.functionTable={abs:{_func:this._functionAbs,_signature:[{types:[c]}]},avg:{_func:this._functionAvg,_signature:[{types:[l]}]},ceil:{_func:this._functionCeil,_signature:[{types:[c]}]},contains:{_func:this._functionContains,_signature:[{types:[d,a]},{types:[r]}]},ends_with:{_func:this._functionEndsWith,_signature:[{types:[d]},{types:[d]}]},floor:{_func:this._functionFloor,_signature:[{types:[c]}]},length:{_func:this._functionLength,_signature:[{types:[d,a,t]}]},map:{_func:this._functionMap,_signature:[{types:[n]},{types:[a]}]},max:{_func:this._functionMax,_signature:[{types:[l,h]}]},merge:{_func:this._functionMerge,_signature:[{types:[t],variadic:!0}]},max_by:{_func:this._functionMaxBy,_signature:[{types:[a]},{types:[n]}]},sum:{_func:this._functionSum,_signature:[{types:[l]}]},starts_with:{_func:this._functionStartsWith,_signature:[{types:[d]},{types:[d]}]},min:{_func:this._functionMin,_signature:[{types:[l,h]}]},min_by:{_func:this._functionMinBy,_signature:[{types:[a]},{types:[n]}]},type:{_func:this._functionType,_signature:[{types:[r]}]},keys:{_func:this._functionKeys,_signature:[{types:[t]}]},values:{_func:this._functionValues,_signature:[{types:[t]}]},sort:{_func:this._functionSort,_signature:[{types:[h,l]}]},sort_by:{_func:this._functionSortBy,_signature:[{types:[a]},{types:[n]}]},join:{_func:this._functionJoin,_signature:[{types:[d]},{types:[h]}]},reverse:{_func:this._functionReverse,_signature:[{types:[d,a]}]},to_array:{_func:this._functionToArray,_signature:[{types:[r]}]},to_string:{_func:this._functionToString,_signature:[{types:[r]}]},to_number:{_func:this._functionToNumber,_signature:[{types:[r]}]},not_null:{_func:this._functionNotNull,_signature:[{types:[r],variadic:!0}]}}}z.EOF=0,z[u]=0,z[p]=0,z[f]=0,z[m]=0,z[v]=0,z[y]=0,z[b]=0,z[I]=0,z[D]=0,z[A]=1,z.Or=2,z.And=3,z.EQ=5,z.GT=5,z.LT=5,z.GTE=5,z.LTE=5,z.NE=5,z[M]=9,z[x]=20,z[C]=21,z.Dot=40,z.Not=45,z[E]=50,z[w]=55,z[T]=60,H.prototype={parse:function(e){this._loadTokens(e),this.index=0;var t=this.expression(0);if("EOF"===this._lookahead(0))return t;var n=this._lookaheadToken(0),i=new Error("Unexpected token type: "+n.type+", value: "+n.value);throw i.name="ParserError",i},_loadTokens:function(e){var t=(new i).tokenize(e);t.push({type:"EOF",value:"",start:e.length}),this.tokens=t},expression:function(e){var t=this._lookaheadToken(0);this._advance();for(var n=this.nud(t),i=this._lookahead(0);e<z[i];)this._advance(),n=this.led(i,n),i=this._lookahead(0);return n},_lookahead:function(e){return this.tokens[this.index+e].type},_lookaheadToken:function(e){return this.tokens[this.index+e]},_advance:function(){this.index++},nud:function(e){var t,n;switch(e.type){case j:return{type:"Literal",value:e.value};case u:return{type:"Field",name:e.value};case p:var i={type:"Field",name:e.value};if(this._lookahead(0)===T)throw new Error("Quoted identifier not allowed for function names.");return i;case"Not":return{type:"NotExpression",children:[t=this.expression(z.Not)]};case x:return t=null,{type:"ValueProjection",children:[{type:"Identity"},t=this._lookahead(0)===f?{type:"Identity"}:this._parseProjectionRHS(z.Star)]};case C:return this.led(e.type,{type:"Identity"});case E:return this._parseMultiselectHash();case M:return{type:"Projection",children:[{type:M,children:[{type:"Identity"}]},t=this._parseProjectionRHS(z.Flatten)]};case w:return this._lookahead(0)===b||this._lookahead(0)===g?(t=this._parseIndexExpression(),this._projectIfSlice({type:"Identity"},t)):this._lookahead(0)===x&&this._lookahead(1)===f?(this._advance(),this._advance(),{type:"Projection",children:[{type:"Identity"},t=this._parseProjectionRHS(z.Star)]}):this._parseMultiselectList();case I:return{type:I};case D:return{type:"ExpressionReference",children:[n=this.expression(z.Expref)]};case T:for(var o=[];this._lookahead(0)!==m;)this._lookahead(0)===I?(n={type:I},this._advance()):n=this.expression(0),o.push(n);return this._match(m),o[0];default:this._errorToken(e)}},led:function(e,t){var n;switch(e){case _:var i=z.Dot;return this._lookahead(0)!==x?{type:"Subexpression",children:[t,n=this._parseDotRHS(i)]}:(this._advance(),{type:"ValueProjection",children:[t,n=this._parseProjectionRHS(i)]});case A:return n=this.expression(z.Pipe),{type:A,children:[t,n]};case"Or":return{type:"OrExpression",children:[t,n=this.expression(z.Or)]};case"And":return{type:"AndExpression",children:[t,n=this.expression(z.And)]};case T:for(var o,r=t.name,s=[];this._lookahead(0)!==m;)this._lookahead(0)===I?(o={type:I},this._advance()):o=this.expression(0),this._lookahead(0)===v&&this._match(v),s.push(o);return this._match(m),{type:"Function",name:r,children:s};case C:var a=this.expression(0);return this._match(f),{type:"FilterProjection",children:[t,n=this._lookahead(0)===M?{type:"Identity"}:this._parseProjectionRHS(z.Filter),a]};case M:return{type:"Projection",children:[{type:M,children:[t]},this._parseProjectionRHS(z.Flatten)]};case"EQ":case"NE":case"GT":case P:case"LT":case F:return this._parseComparator(t,e);case w:var l=this._lookaheadToken(0);return l.type===b||l.type===g?(n=this._parseIndexExpression(),this._projectIfSlice(t,n)):(this._match(x),this._match(f),{type:"Projection",children:[t,n=this._parseProjectionRHS(z.Star)]});default:this._errorToken(this._lookaheadToken(0))}},_match:function(e){if(this._lookahead(0)!==e){var t=this._lookaheadToken(0),n=new Error("Expected "+e+", got: "+t.type);throw n.name="ParserError",n}this._advance()},_errorToken:function(e){var t=new Error("Invalid token ("+e.type+'): "'+e.value+'"');throw t.name="ParserError",t},_parseIndexExpression:function(){if(this._lookahead(0)===g||this._lookahead(1)===g)return this._parseSliceExpression();var e={type:"Index",value:this._lookaheadToken(0).value};return this._advance(),this._match(f),e},_projectIfSlice:function(e,t){var n={type:"IndexExpression",children:[e,t]};return"Slice"===t.type?{type:"Projection",children:[n,this._parseProjectionRHS(z.Star)]}:n},_parseSliceExpression:function(){for(var e=[null,null,null],t=0,n=this._lookahead(0);n!==f&&t<3;){if(n===g)t++,this._advance();else{if(n!==b){var i=this._lookahead(0),o=new Error("Syntax error, unexpected token: "+i.value+"("+i.type+")");throw o.name="Parsererror",o}e[t]=this._lookaheadToken(0).value,this._advance()}n=this._lookahead(0)}return this._match(f),{type:"Slice",children:e}},_parseComparator:function(e,t){return{type:"Comparator",name:t,children:[e,this.expression(z[t])]}},_parseDotRHS:function(e){var t=this._lookahead(0);return 0<=[u,p,x].indexOf(t)?this.expression(e):t===w?(this._match(w),this._parseMultiselectList()):t===E?(this._match(E),this._parseMultiselectHash()):void 0},_parseProjectionRHS:function(e){var t;if(z[this._lookahead(0)]<10)t={type:"Identity"};else if(this._lookahead(0)===w)t=this.expression(e);else if(this._lookahead(0)===C)t=this.expression(e);else{if(this._lookahead(0)!==_){var n=this._lookaheadToken(0),i=new Error("Sytanx error, unexpected token: "+n.value+"("+n.type+")");throw i.name="ParserError",i}this._match(_),t=this._parseDotRHS(e)}return t},_parseMultiselectList:function(){for(var e=[];this._lookahead(0)!==f;){var t=this.expression(0);if(e.push(t),this._lookahead(0)===v&&(this._match(v),this._lookahead(0)===f))throw new Error("Unexpected token Rbracket")}return this._match(f),{type:"MultiSelectList",children:e}},_parseMultiselectHash:function(){for(var e,t,n,i=[],o=[u,p];;){if(e=this._lookaheadToken(0),o.indexOf(e.type)<0)throw new Error("Expecting an identifier token, got: "+e.type);if(t=e.value,this._advance(),this._match(g),n={type:"KeyValuePair",name:t,value:this.expression(0)},i.push(n),this._lookahead(0)===v)this._match(v);else if(this._lookahead(0)===y){this._match(y);break}}return{type:"MultiSelectHash",children:i}}},U.prototype={search:function(e,t){return this.visit(e,t)},visit:function(e,t){var n,i,o,r,s,a;switch(e.type){case"Field":return null===t||!N(t)||void 0===(s=t[e.name])?null:s;case"Subexpression":for(f=this.visit(e.children[0],t),g=1;g<e.children.length;g++)if(null===(f=this.visit(e.children[1],f)))return null;return f;case"IndexExpression":return a=this.visit(e.children[0],t),this.visit(e.children[1],a);case"Index":if(!S(t))return null;var l=e.value;return l<0&&(l=t.length+l),void 0===(f=t[l])&&(f=null),f;case"Slice":if(!S(t))return null;var c=e.children.slice(0),d=this.computeSliceParams(t.length,c),h=d[0],u=d[1],p=d[2],f=[];if(0<p)for(g=h;g<u;g+=p)f.push(t[g]);else for(g=h;u<g;g+=p)f.push(t[g]);return f;case"Projection":var m=this.visit(e.children[0],t);if(!S(m))return null;for(w=[],g=0;g<m.length;g++)null!==(i=this.visit(e.children[1],m[g]))&&w.push(i);return w;case"ValueProjection":if(!N(m=this.visit(e.children[0],t)))return null;w=[];for(var v=function(e){for(var t=Object.keys(e),n=[],i=0;i<t.length;i++)n.push(e[t[i]]);return n}(m),g=0;g<v.length;g++)null!==(i=this.visit(e.children[1],v[g]))&&w.push(i);return w;case"FilterProjection":if(!S(m=this.visit(e.children[0],t)))return null;var y=[],b=[];for(g=0;g<m.length;g++)O(n=this.visit(e.children[2],m[g]))||y.push(m[g]);for(var x=0;x<y.length;x++)null!==(i=this.visit(e.children[1],y[x]))&&b.push(i);return b;case"Comparator":switch(o=this.visit(e.children[0],t),r=this.visit(e.children[1],t),e.name){case"EQ":f=k(o,r);break;case"NE":f=!k(o,r);break;case"GT":f=r<o;break;case P:f=r<=o;break;case"LT":f=o<r;break;case F:f=o<=r;break;default:throw new Error("Unknown comparator: "+e.name)}return f;case M:var C=this.visit(e.children[0],t);if(!S(C))return null;var _=[];for(g=0;g<C.length;g++)S(i=C[g])?_.push.apply(_,i):_.push(i);return _;case"Identity":return t;case"MultiSelectList":if(null===t)return null;for(w=[],g=0;g<e.children.length;g++)w.push(this.visit(e.children[g],t));return w;case"MultiSelectHash":if(null===t)return null;var E,w={};for(g=0;g<e.children.length;g++)w[(E=e.children[g]).name]=this.visit(E.value,t);return w;case"OrExpression":return O(n=this.visit(e.children[0],t))&&(n=this.visit(e.children[1],t)),n;case"AndExpression":return!0===O(o=this.visit(e.children[0],t))?o:this.visit(e.children[1],t);case"NotExpression":return O(o=this.visit(e.children[0],t));case"Literal":return e.value;case A:return a=this.visit(e.children[0],t),this.visit(e.children[1],a);case I:return t;case"Function":var T=[];for(g=0;g<e.children.length;g++)T.push(this.visit(e.children[g],t));return this.runtime.callFunction(e.name,T);case"ExpressionReference":var j=e.children[0];return j.jmespathType=D,j;default:throw new Error("Unknown node type: "+e.type)}},computeSliceParams:function(e,t){var n=t[0],i=t[1],o=t[2],r=[null,null,null];if(null===o)o=1;else if(0===o){var s=new Error("Invalid slice, step cannot be 0");throw s.name="RuntimeError",s}var a=o<0,n=null===n?a?e-1:0:this.capSliceRange(e,n,o),i=null===i?a?-1:e:this.capSliceRange(e,i,o);return r[0]=n,r[1]=i,r[2]=o,r},capSliceRange:function(e,t,n){return t<0?(t+=e)<0&&(t=n<0?-1:0):e<=t&&(t=n<0?e-1:e),t}},J.prototype={callFunction:function(e,t){var n=this.functionTable[e];if(void 0===n)throw new Error("Unknown function: "+e+"()");return this._validateArgs(e,t,n._signature),n._func.call(this,t)},_validateArgs:function(e,t,n){var i,o,r,s;if(n[n.length-1].variadic){if(t.length<n.length)throw i=1===n.length?" argument":" arguments",new Error("ArgumentError: "+e+"() takes at least"+n.length+i+" but received "+t.length)}else if(t.length!==n.length)throw i=1===n.length?" argument":" arguments",new Error("ArgumentError: "+e+"() takes "+n.length+i+" but received "+t.length);for(var a=0;a<n.length;a++){s=!1,o=n[a].types,r=this._getTypeName(t[a]);for(var l=0;l<o.length;l++)if(this._typeMatches(r,o[l],t[a])){s=!0;break}if(!s)throw new Error("TypeError: "+e+"() expected argument "+(a+1)+" to be type "+o+" but received type "+r+" instead.")}},_typeMatches:function(e,t,n){if(t===r)return!0;if(t!==h&&t!==l&&t!==a)return e===t;if(t===a)return e===a;if(e===a){var i;t===l?i=c:t===h&&(i=d);for(var o=0;o<n.length;o++)if(!this._typeMatches(this._getTypeName(n[o]),i,n[o]))return!1;return!0}},_getTypeName:function(e){switch(Object.prototype.toString.call(e)){case"[object String]":return d;case"[object Number]":return c;case"[object Array]":return a;case"[object Boolean]":return 5;case"[object Null]":return 7;case"[object Object]":return e.jmespathType===D?n:t}},_functionStartsWith:function(e){return 0===e[0].lastIndexOf(e[1])},_functionEndsWith:function(e){var t=e[0],n=e[1];return-1!==t.indexOf(n,t.length-n.length)},_functionReverse:function(e){if(this._getTypeName(e[0])===d){for(var t=e[0],n="",i=t.length-1;0<=i;i--)n+=t[i];return n}var o=e[0].slice(0);return o.reverse(),o},_functionAbs:function(e){return Math.abs(e[0])},_functionCeil:function(e){return Math.ceil(e[0])},_functionAvg:function(e){for(var t=0,n=e[0],i=0;i<n.length;i++)t+=n[i];return t/n.length},_functionContains:function(e){return 0<=e[0].indexOf(e[1])},_functionFloor:function(e){return Math.floor(e[0])},_functionLength:function(e){return N(e[0])?Object.keys(e[0]).length:e[0].length},_functionMap:function(e){for(var t=[],n=this._interpreter,i=e[0],o=e[1],r=0;r<o.length;r++)t.push(n.visit(i,o[r]));return t},_functionMerge:function(e){for(var t={},n=0;n<e.length;n++){var i=e[n];for(var o in i)t[o]=i[o]}return t},_functionMax:function(e){if(0<e[0].length){if(this._getTypeName(e[0][0])===c)return Math.max.apply(Math,e[0]);for(var t=e[0],n=t[0],i=1;i<t.length;i++)n.localeCompare(t[i])<0&&(n=t[i]);return n}return null},_functionMin:function(e){if(0<e[0].length){if(this._getTypeName(e[0][0])===c)return Math.min.apply(Math,e[0]);for(var t=e[0],n=t[0],i=1;i<t.length;i++)t[i].localeCompare(n)<0&&(n=t[i]);return n}return null},_functionSum:function(e){for(var t=0,n=e[0],i=0;i<n.length;i++)t+=n[i];return t},_functionType:function(e){switch(this._getTypeName(e[0])){case c:return"number";case d:return"string";case a:return"array";case t:return"object";case 5:return"boolean";case n:return"expref";case 7:return"null"}},_functionKeys:function(e){return Object.keys(e[0])},_functionValues:function(e){for(var t=e[0],n=Object.keys(t),i=[],o=0;o<n.length;o++)i.push(t[n[o]]);return i},_functionJoin:function(e){var t=e[0];return e[1].join(t)},_functionToArray:function(e){return this._getTypeName(e[0])===a?e[0]:[e[0]]},_functionToString:function(e){return this._getTypeName(e[0])===d?e[0]:JSON.stringify(e[0])},_functionToNumber:function(e){var t,n=this._getTypeName(e[0]);return n===c?e[0]:n!==d||(t=+e[0],isNaN(t))?null:t},_functionNotNull:function(e){for(var t=0;t<e.length;t++)if(7!==this._getTypeName(e[t]))return e[t];return null},_functionSort:function(e){var t=e[0].slice(0);return t.sort(),t},_functionSortBy:function(e){var t=e[0].slice(0);if(0===t.length)return t;var o=this._interpreter,r=e[1],s=this._getTypeName(o.visit(r,t[0]));if([c,d].indexOf(s)<0)throw new Error("TypeError");for(var a=this,n=[],i=0;i<t.length;i++)n.push([i,t[i]]);n.sort(function(e,t){var n=o.visit(r,e[1]),i=o.visit(r,t[1]);if(a._getTypeName(n)!==s)throw new Error("TypeError: expected "+s+", received "+a._getTypeName(n));if(a._getTypeName(i)!==s)throw new Error("TypeError: expected "+s+", received "+a._getTypeName(i));return i<n?1:n<i?-1:e[0]-t[0]});for(var l=0;l<n.length;l++)t[l]=n[l][1];return t},_functionMaxBy:function(e){for(var t,n,i=e[1],o=e[0],r=this.createKeyFunction(i,[c,d]),s=-1/0,a=0;a<o.length;a++)s<(n=r(o[a]))&&(s=n,t=o[a]);return t},_functionMinBy:function(e){for(var t,n,i=e[1],o=e[0],r=this.createKeyFunction(i,[c,d]),s=1/0,a=0;a<o.length;a++)(n=r(o[a]))<s&&(s=n,t=o[a]);return t},createKeyFunction:function(i,o){var r=this,s=this._interpreter;return function(e){var t=s.visit(i,e);if(o.indexOf(r._getTypeName(t))<0){var n="TypeError: expected one of "+o+", received "+r._getTypeName(t);throw new Error(n)}return t}}},e.tokenize=function(e){return(new i).tokenize(e)},e.compile=function(e){return(new H).parse(e)},e.search=function(e,t){var n=new H,i=new J,o=new U(i);i._interpreter=o;var r=n.parse(t);return o.search(r,e)},e.strictDeepEqual=k}(t)},function(e,t,n){t.tryRequireThemeJsonEditor=function(){try{n(23)}catch(e){console.error(e)}}},function(e,t,o){"use strict";var n=o(15),i=o(12),r=o(25).treeModeMixins,s=o(16).textModeMixins,a=o(26).previewModeMixins,l=o(0),c=l.clear,d=l.extend,h=l.getInnerText,u=l.getInternetExplorerVersion,p=l.parse,f=o(24).tryRequireAjv,m=o(6).showTransformModal,v=o(5).showSortModal,g=f();function y(e,t,n){if(!(this instanceof y))throw new Error('JSONEditor constructor called without "new".');var i=u();if(-1!==i&&i<9)throw new Error("Unsupported browser, IE9 or newer required. Please install the newest version of your browser.");t&&(t.error&&(console.warn('Option "error" has been renamed to "onError"'),t.onError=t.error,delete t.error),t.change&&(console.warn('Option "change" has been renamed to "onChange"'),t.onChange=t.change,delete t.change),t.editable&&(console.warn('Option "editable" has been renamed to "onEditable"'),t.onEditable=t.editable,delete t.editable),t.onChangeJSON&&("text"!==t.mode&&"code"!==t.mode&&(!t.modes||-1===t.modes.indexOf("text")&&-1===t.modes.indexOf("code"))||console.warn('Option "onChangeJSON" is not applicable to modes "text" and "code". Use "onChangeText" or "onChange" instead.')),t&&Object.keys(t).forEach(function(e){-1===y.VALID_OPTIONS.indexOf(e)&&console.warn('Unknown option "'+e+'". This option will be ignored')})),arguments.length&&this._create(e,t,n)}"undefined"==typeof Promise&&console.error("Promise undefined. Please load a Promise polyfill in the browser in order to use JSONEditor"),y.modes={},y.prototype.DEBOUNCE_INTERVAL=150,y.VALID_OPTIONS=["ajv","schema","schemaRefs","templates","ace","theme","autocomplete","onChange","onChangeJSON","onChangeText","onEditable","onError","onEvent","onModeChange","onNodeName","onValidate","onCreateMenu","onSelectionChange","onTextSelectionChange","onClassName","onFocus","onBlur","colorPicker","onColorPicker","timestampTag","timestampFormat","escapeUnicode","history","search","mode","modes","name","indentation","sortObjectKeys","navigationBar","statusBar","mainMenuBar","languages","language","enableSort","enableTransform","limitDragging","maxVisibleChilds","onValidationError","modalAnchor","popupAnchor","createQuery","executeQuery","queryDescription"],y.prototype._create=function(e,t,n){this.container=e,this.options=t||{},this.json=n||{};var i=this.options.mode||this.options.modes&&this.options.modes[0]||"tree";this.setMode(i)},y.prototype.destroy=function(){},y.prototype.set=function(e){this.json=e},y.prototype.get=function(){return this.json},y.prototype.setText=function(e){this.json=p(e)},y.prototype.getText=function(){return JSON.stringify(this.json)},y.prototype.setName=function(e){this.options||(this.options={}),this.options.name=e},y.prototype.getName=function(){return this.options&&this.options.name},y.prototype.setMode=function(e){if(e!==this.options.mode||!this.create){var t=this.container,n=d({},this.options),i=n.mode;n.mode=e;var o=y.modes[e];if(!o)throw new Error('Unknown mode "'+n.mode+'"');try{var r="text"===o.data,s=this.getName(),a=this[r?"getText":"get"]();if(this.destroy(),c(this),d(this,o.mixin),this.create(t,n),this.setName(s),this[r?"setText":"set"](a),"function"==typeof o.load)try{o.load.call(this)}catch(e){console.error(e)}if("function"==typeof n.onModeChange&&e!==i)try{n.onModeChange(e,i)}catch(e){console.error(e)}}catch(e){this._onError(e)}}},y.prototype.getMode=function(){return this.options.mode},y.prototype._onError=function(e){if(!this.options||"function"!=typeof this.options.onError)throw e;this.options.onError(e)},y.prototype.setSchema=function(e,t){if(e){var n;try{this.options.ajv?n=this.options.ajv:((n=g({allErrors:!0,verbose:!0,schemaId:"auto",$data:!0})).addMetaSchema(o(!function(){var e=new Error("Cannot find module 'ajv/lib/refs/json-schema-draft-04.json'");throw e.code="MODULE_NOT_FOUND",e}())),n.addMetaSchema(o(!function(){var e=new Error("Cannot find module 'ajv/lib/refs/json-schema-draft-06.json'");throw e.code="MODULE_NOT_FOUND",e}())))}catch(e){console.warn("Failed to create an instance of Ajv, JSON Schema validation is not available. Please use a JSONEditor bundle including Ajv, or pass an instance of Ajv as via the configuration option `ajv`.")}if(n){if(t){for(var i in t)n.removeSchema(i),t[i]&&n.addSchema(t[i],i);this.options.schemaRefs=t}this.validateSchema=n.compile(e),this.options.schema=e,this.validate()}this.refresh()}else this.validateSchema=null,this.options.schema=null,this.options.schemaRefs=null,this.validate(),this.refresh()},y.prototype.validate=function(){},y.prototype.refresh=function(){},y.registerMode=function(e){var t;if(Array.isArray(e))for(o=0;o<e.length;o++)y.registerMode(e[o]);else{if(!("mode"in e))throw new Error('Property "mode" missing');if(!("mixin"in e))throw new Error('Property "mixin" missing');if(!("data"in e))throw new Error('Property "data" missing');var n=e.mode;if(n in y.modes)throw new Error('Mode "'+n+'" already registered');if("function"!=typeof e.mixin.create)throw new Error('Required function "create" missing on mixin');for(var i=["setMode","registerMode","modes"],o=0;o<i.length;o++)if((t=i[o])in e.mixin)throw new Error('Reserved property "'+t+'" not allowed in mixin');y.modes[n]=e}},y.registerMode(r),y.registerMode(s),y.registerMode(a),y.ace=n,y.Ajv=g,y.VanillaPicker=i,y.showTransformModal=m,y.showSortModal=v,y.getInnerText=h,y.default=y,e.exports=y},function(e,t){window.ace.define("ace/theme/jsoneditor",["require","exports","module","ace/lib/dom"],function(e,t,n){t.isDark=!1,t.cssClass="ace-jsoneditor",t.cssText='.ace-jsoneditor .ace_gutter {\nbackground: #ebebeb;\ncolor: #333\n}\n\n.ace-jsoneditor.ace_editor {\nfont-family: "dejavu sans mono", "droid sans mono", consolas, monaco, "lucida console", "courier new", courier, monospace, sans-serif;\nline-height: 1.3;\nbackground-color: #fff;\n}\n.ace-jsoneditor .ace_print-margin {\nwidth: 1px;\nbackground: #e8e8e8\n}\n.ace-jsoneditor .ace_scroller {\nbackground-color: #FFFFFF\n}\n.ace-jsoneditor .ace_text-layer {\ncolor: gray\n}\n.ace-jsoneditor .ace_variable {\ncolor: #1a1a1a\n}\n.ace-jsoneditor .ace_cursor {\nborder-left: 2px solid #000000\n}\n.ace-jsoneditor .ace_overwrite-cursors .ace_cursor {\nborder-left: 0px;\nborder-bottom: 1px solid #000000\n}\n.ace-jsoneditor .ace_marker-layer .ace_selection {\nbackground: lightgray\n}\n.ace-jsoneditor.ace_multiselect .ace_selection.ace_start {\nbox-shadow: 0 0 3px 0px #FFFFFF;\nborder-radius: 2px\n}\n.ace-jsoneditor .ace_marker-layer .ace_step {\nbackground: rgb(255, 255, 0)\n}\n.ace-jsoneditor .ace_marker-layer .ace_bracket {\nmargin: -1px 0 0 -1px;\nborder: 1px solid #BFBFBF\n}\n.ace-jsoneditor .ace_marker-layer .ace_active-line {\nbackground: #FFFBD1\n}\n.ace-jsoneditor .ace_gutter-active-line {\nbackground-color : #dcdcdc\n}\n.ace-jsoneditor .ace_marker-layer .ace_selected-word {\nborder: 1px solid lightgray\n}\n.ace-jsoneditor .ace_invisible {\ncolor: #BFBFBF\n}\n.ace-jsoneditor .ace_keyword,\n.ace-jsoneditor .ace_meta,\n.ace-jsoneditor .ace_support.ace_constant.ace_property-value {\ncolor: #AF956F\n}\n.ace-jsoneditor .ace_keyword.ace_operator {\ncolor: #484848\n}\n.ace-jsoneditor .ace_keyword.ace_other.ace_unit {\ncolor: #96DC5F\n}\n.ace-jsoneditor .ace_constant.ace_language {\ncolor: darkorange\n}\n.ace-jsoneditor .ace_constant.ace_numeric {\ncolor: red\n}\n.ace-jsoneditor .ace_constant.ace_character.ace_entity {\ncolor: #BF78CC\n}\n.ace-jsoneditor .ace_invalid {\ncolor: #FFFFFF;\nbackground-color: #FF002A;\n}\n.ace-jsoneditor .ace_fold {\nbackground-color: #AF956F;\nborder-color: #000000\n}\n.ace-jsoneditor .ace_storage,\n.ace-jsoneditor .ace_support.ace_class,\n.ace-jsoneditor .ace_support.ace_function,\n.ace-jsoneditor .ace_support.ace_other,\n.ace-jsoneditor .ace_support.ace_type {\ncolor: #C52727\n}\n.ace-jsoneditor .ace_string {\ncolor: green\n}\n.ace-jsoneditor .ace_comment {\ncolor: #BCC8BA\n}\n.ace-jsoneditor .ace_entity.ace_name.ace_tag,\n.ace-jsoneditor .ace_entity.ace_other.ace_attribute-name {\ncolor: #606060\n}\n.ace-jsoneditor .ace_markup.ace_underline {\ntext-decoration: underline\n}\n.ace-jsoneditor .ace_indent-guide {\nbackground: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bLly//BwAmVgd1/w11/gAAAABJRU5ErkJggg==") right repeat-y\n}',e("../lib/dom").importCssString(t.cssText,t.cssClass)})},function(e,t,n){t.tryRequireAjv=function(){try{return n(!function(){var e=new Error("Cannot find module 'ajv'");throw e.code="MODULE_NOT_FOUND",e}())}catch(e){}}},function(e,t,n){"use strict";n.r(t),n.d(t,"treeModeMixins",function(){return L});var i=n(12),r=n.n(i);function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var s=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.locked=!1}var t,n,i;return t=e,(n=[{key:"highlight",value:function(e){this.locked||(this.node!==e&&(this.node&&this.node.setHighlight(!1),this.node=e,this.node.setHighlight(!0)),this._cancelUnhighlight())}},{key:"unhighlight",value:function(){var e;this.locked||(e=this).node&&(this._cancelUnhighlight(),this.unhighlightTimer=setTimeout(function(){e.node.setHighlight(!1),e.node=void 0,e.unhighlightTimer=void 0},0))}},{key:"_cancelUnhighlight",value:function(){this.unhighlightTimer&&(clearTimeout(this.unhighlightTimer),this.unhighlightTimer=void 0)}},{key:"lock",value:function(){this.locked=!0}},{key:"unlock",value:function(){this.locked=!1}}])&&o(t.prototype,n),i&&o(t,i),e}(),P=n(0);function a(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var l=function(){function e(t){function r(e){return t.node.findNodeByInternalPath(e)}!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.editor=t,this.history=[],this.index=-1,this.clear(),this.actions={editField:{undo:function(e){r(e.parentPath).childs[e.index].updateField(e.oldValue)},redo:function(e){r(e.parentPath).childs[e.index].updateField(e.newValue)}},editValue:{undo:function(e){r(e.path).updateValue(e.oldValue)},redo:function(e){r(e.path).updateValue(e.newValue)}},changeType:{undo:function(e){r(e.path).changeType(e.oldType)},redo:function(e){r(e.path).changeType(e.newType)}},appendNodes:{undo:function(e){var t=r(e.parentPath);e.paths.map(r).forEach(function(e){t.removeChild(e)})},redo:function(e){var t=r(e.parentPath);e.nodes.forEach(function(e){t.appendChild(e)})}},insertBeforeNodes:{undo:function(e){var t=r(e.parentPath);e.paths.map(r).forEach(function(e){t.removeChild(e)})},redo:function(e){var t=r(e.parentPath),n=r(e.beforePath);e.nodes.forEach(function(e){t.insertBefore(e,n)})}},insertAfterNodes:{undo:function(e){var t=r(e.parentPath);e.paths.map(r).forEach(function(e){t.removeChild(e)})},redo:function(e){var t=r(e.parentPath),n=r(e.afterPath);e.nodes.forEach(function(e){t.insertAfter(e,n),n=e})}},removeNodes:{undo:function(e){var t=r(e.parentPath),n=t.childs[e.index]||t.append;e.nodes.forEach(function(e){t.insertBefore(e,n)})},redo:function(e){var t=r(e.parentPath);e.paths.map(r).forEach(function(e){t.removeChild(e)})}},duplicateNodes:{undo:function(e){var t=r(e.parentPath);e.clonePaths.map(r).forEach(function(e){t.removeChild(e)})},redo:function(e){var i=r(e.parentPath),o=r(e.afterPath);e.paths.map(r).forEach(function(e){var t,n=e.clone();"object"===i.type&&(t=i.getFieldNames(),n.field=Object(P.findUniqueName)(e.field,t)),i.insertAfter(n,o),o=n})}},moveNodes:{undo:function(n){var i=r(n.oldParentPath),e=r(n.newParentPath),o=i.childs[n.oldIndex]||i.append;e.childs.slice(n.newIndex,n.newIndex+n.count).forEach(function(e,t){e.field=n.fieldNames[t],i.moveBefore(e,o)}),null===n.newParentPathRedo&&(n.newParentPathRedo=e.getInternalPath())},redo:function(n){var e=r(n.oldParentPathRedo),i=r(n.newParentPathRedo),o=i.childs[n.newIndexRedo]||i.append;e.childs.slice(n.oldIndexRedo,n.oldIndexRedo+n.count).forEach(function(e,t){e.field=n.fieldNames[t],i.moveBefore(e,o)})}},sort:{undo:function(e){var t=r(e.path);t.hideChilds(),t.childs=e.oldChilds,t.updateDom({updateIndexes:!0}),t.showChilds()},redo:function(e){var t=r(e.path);t.hideChilds(),t.childs=e.newChilds,t.updateDom({updateIndexes:!0}),t.showChilds()}},transform:{undo:function(e){r(e.path).setInternalValue(e.oldValue)},redo:function(e){r(e.path).setInternalValue(e.newValue)}}}}var t,n,i;return t=e,(n=[{key:"onChange",value:function(){}},{key:"add",value:function(e,t){this.index++,this.history[this.index]={action:e,params:t,timestamp:new Date},this.index<this.history.length-1&&this.history.splice(this.index+1,this.history.length-this.index-1),this.onChange()}},{key:"clear",value:function(){this.history=[],this.index=-1,this.onChange()}},{key:"canUndo",value:function(){return 0<=this.index}},{key:"canRedo",value:function(){return this.index<this.history.length-1}},{key:"undo",value:function(){if(this.canUndo()){var e=this.history[this.index];if(e){var t=this.actions[e.action];if(t&&t.undo){if(t.undo(e.params),e.params.oldSelection)try{this.editor.setDomSelection(e.params.oldSelection)}catch(e){console.error(e)}}else console.error(new Error('unknown action "'+e.action+'"'))}this.index--,this.onChange()}}},{key:"redo",value:function(){if(this.canRedo()){this.index++;var e=this.history[this.index];if(e){var t=this.actions[e.action];if(t&&t.redo){if(t.redo(e.params),e.params.newSelection)try{this.editor.setDomSelection(e.params.newSelection)}catch(e){console.error(e)}}else console.error(new Error('unknown action "'+e.action+'"'))}this.onChange()}}},{key:"destroy",value:function(){this.editor=null,this.history=[],this.index=-1}}])&&a(t.prototype,n),i&&a(t,i),e}(),h=n(1);function c(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var d=function(){function d(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d);var n=this;this.editor=e,this.timeout=void 0,this.delay=200,this.lastText=void 0,this.results=null,this.dom={},this.dom.container=t;var i=document.createElement("div");(this.dom.wrapper=i).className="jsoneditor-search",t.appendChild(i);var o=document.createElement("div");(this.dom.results=o).className="jsoneditor-results",i.appendChild(o);var r=document.createElement("div");(this.dom.input=r).className="jsoneditor-frame",r.title=Object(h.c)("searchTitle"),i.appendChild(r);var s=document.createElement("button");s.type="button",s.className="jsoneditor-refresh",r.appendChild(s);var a=document.createElement("input");a.type="text",(this.dom.search=a).oninput=function(e){n._onDelayedSearch(e)},a.onchange=function(e){n._onSearch()},a.onkeydown=function(e){n._onKeyDown(e)},a.onkeyup=function(e){n._onKeyUp(e)},s.onclick=function(e){a.select()},r.appendChild(a);var l=document.createElement("button");l.type="button",l.title=Object(h.c)("searchNextResultTitle"),l.className="jsoneditor-next",l.onclick=function(){n.next()},r.appendChild(l);var c=document.createElement("button");c.type="button",c.title=Object(h.c)("searchPreviousResultTitle"),c.className="jsoneditor-previous",c.onclick=function(){n.previous()},r.appendChild(c)}var e,t,n;return e=d,(t=[{key:"next",value:function(e){var t;this.results&&((t=null!==this.resultIndex?this.resultIndex+1:0)>this.results.length-1&&(t=0),this._setActiveResult(t,e))}},{key:"previous",value:function(e){var t,n;this.results&&(t=this.results.length-1,(n=null!==this.resultIndex?this.resultIndex-1:t)<0&&(n=t),this._setActiveResult(n,e))}},{key:"_setActiveResult",value:function(e,t){var n;if(this.activeResult&&(n=this.activeResult.node,"field"===this.activeResult.elem?delete n.searchFieldActive:delete n.searchValueActive,n.updateDom()),!this.results||!this.results[e])return this.resultIndex=void 0,void(this.activeResult=void 0);this.resultIndex=e;var i=this.results[this.resultIndex].node,o=this.results[this.resultIndex].elem;"field"===o?i.searchFieldActive=!0:i.searchValueActive=!0,this.activeResult=this.results[this.resultIndex],i.updateDom(),i.scrollTo(function(){t&&i.focus(o)})}},{key:"_clearDelay",value:function(){void 0!==this.timeout&&(clearTimeout(this.timeout),delete this.timeout)}},{key:"_onDelayedSearch",value:function(){this._clearDelay();var t=this;this.timeout=setTimeout(function(e){t._onSearch()},this.delay)}},{key:"_onSearch",value:function(e){this._clearDelay();var t=this.dom.search.value,n=0<t.length?t:void 0;if(n!==this.lastText||e){this.lastText=n,this.results=this.editor.search(n);var i,o=this.results[0]?this.results[0].node.MAX_SEARCH_RESULTS:1/0,r=0;if(this.activeResult)for(var s=0;s<this.results.length;s++)if(this.results[s].node===this.activeResult.node){r=s;break}this._setActiveResult(r,!1),void 0!==n?(i=this.results.length,this.dom.results.textContent=0===i?"no results":1===i?"1 result":o<i?o+"+ results":i+" results"):this.dom.results.textContent=""}}},{key:"_onKeyDown",value:function(e){var t=e.which;27===t?(this.dom.search.value="",this._onSearch(),e.preventDefault(),e.stopPropagation()):13===t&&(e.ctrlKey?this._onSearch(!0):e.shiftKey?this.previous():this.next(),e.preventDefault(),e.stopPropagation())}},{key:"_onKeyUp",value:function(e){var t=e.keyCode;27!==t&&13!==t&&this._onDelayedSearch(e)}},{key:"clear",value:function(){this.dom.search.value="",this._onSearch()}},{key:"forceSearch",value:function(){this._onSearch(!0)}},{key:"isEmpty",value:function(){return""===this.dom.search.value}},{key:"destroy",value:function(){this.editor=null,this.dom.container.removeChild(this.dom.wrapper),this.dom=null,this.results=null,this.activeResult=null,this._clearDelay()}}])&&c(e.prototype,t),n&&c(e,n),d}(),u=n(3);function p(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var f=function(){function n(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),e&&(this.root=t,this.path=document.createElement("div"),this.path.className="jsoneditor-treepath",this.path.setAttribute("tabindex",0),this.contentMenuClicked=!1,e.appendChild(this.path),this.reset())}var e,t,i;return e=n,(t=[{key:"reset",value:function(){this.path.textContent=Object(h.c)("selectNode")}},{key:"setPath",value:function(s){var a=this;this.path.textContent="",s&&s.length&&s.forEach(function(n,i){var e,t,o,r=document.createElement("span");r.className="jsoneditor-treepath-element",r.innerText=n.name,r.onclick=function(e){this.selectionCallback&&this.selectionCallback(e)}.bind(a,n),a.path.appendChild(r),n.children.length&&((e=document.createElement("span")).className="jsoneditor-treepath-seperator",e.textContent="►",e.onclick=function(){a.contentMenuClicked=!0;var t=[];n.children.forEach(function(e){t.push({text:e.name,className:"jsoneditor-type-modes"+(s[i+1]+1&&s[i+1].name===e.name?" jsoneditor-selected":""),click:function(e,t){this.contextMenuCallback&&this.contextMenuCallback(e,t)}.bind(a,n,e.name)})}),new u.a(t).show(e,a.root,!0)},a.path.appendChild(e)),i===s.length-1&&(t=(e||r).getBoundingClientRect().right,a.path.offsetWidth<t&&(a.path.scrollLeft=t),a.path.scrollLeft&&((o=document.createElement("span")).className="jsoneditor-treepath-show-all-btn",o.title="show all path",o.textContent="...",o.onclick=function(e){a.contentMenuClicked=!1,Object(P.addClassName)(a.path,"show-all"),a.path.style.width=a.path.parentNode.getBoundingClientRect().width-10+"px",a.path.onblur=function(){if(a.contentMenuClicked)return a.contentMenuClicked=!1,void a.path.focus();Object(P.removeClassName)(a.path,"show-all"),a.path.onblur=void 0,a.path.style.width="",a.setPath(e)}}.bind(a,s),a.path.insertBefore(o,a.path.firstChild)))})}},{key:"onSectionSelected",value:function(e){"function"==typeof e&&(this.selectionCallback=e)}},{key:"onContextMenuItemSelected",value:function(e){"function"==typeof e&&(this.contextMenuCallback=e)}}])&&p(e.prototype,t),i&&p(e,i),n}(),m=n(10),v=n.n(m),g=n(11);var y=n(5),b=n(6),x=n(2),C=void 0;function _(e){return(_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function E(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var S=function(){function A(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,A),this.editor=e,this.dom={},this.expanded=!1,t&&t instanceof Object?(this.setField(t.field,t.fieldEditable),"value"in t&&this.setValue(t.value,t.type),"internalValue"in t&&this.setInternalValue(t.internalValue)):(this.setField(""),this.setValue(null)),this._debouncedOnChangeValue=Object(P.debounce)(this._onChangeValue.bind(this),A.prototype.DEBOUNCE_INTERVAL),this._debouncedOnChangeField=Object(P.debounce)(this._onChangeField.bind(this),A.prototype.DEBOUNCE_INTERVAL),this.visibleChilds=this.getMaxVisibleChilds()}var e,t,n;return e=A,(t=[{key:"getMaxVisibleChilds",value:function(){return this.editor&&this.editor.options&&this.editor.options.maxVisibleChilds?this.editor.options.maxVisibleChilds:w}},{key:"_updateEditability",value:function(){var e;this.editable={field:!0,value:!0},this.editor&&(this.editable.field="tree"===this.editor.options.mode,this.editable.value="view"!==this.editor.options.mode,"tree"!==this.editor.options.mode&&"form"!==this.editor.options.mode||"function"!=typeof this.editor.options.onEditable||("boolean"==typeof(e=this.editor.options.onEditable({field:this.field,value:this.value,path:this.getPath()}))?(this.editable.field=e,this.editable.value=e):("boolean"==typeof e.field&&(this.editable.field=e.field),"boolean"==typeof e.value&&(this.editable.value=e.value))))}},{key:"getPath",value:function(){for(var e=this,t=[];e;){var n=e.getName();void 0!==n&&t.unshift(n),e=e.parent}return t}},{key:"getInternalPath",value:function(){for(var e=this,t=[];e;)e.parent&&t.unshift(e.getIndex()),e=e.parent;return t}},{key:"getName",value:function(){return this.parent?"array"!==this.parent.type?this.field:this.index:void 0}},{key:"findNodeByPath",value:function(e){if(e){if(0===e.length)return this;if(e.length&&this.childs&&this.childs.length)for(var t=0;t<this.childs.length;++t)if(""+e[0]==""+this.childs[t].getName())return this.childs[t].findNodeByPath(e.slice(1))}}},{key:"findNodeByInternalPath",value:function(e){if(e){for(var t=this,n=0;n<e.length&&t;n++)var i=e[n],t=t.childs[i];return t}}},{key:"serialize",value:function(){return{value:this.getValue(),path:this.getPath()}}},{key:"findNode",value:function(e){for(var n=Object(P.parsePath)(e),i=this;i&&0<n.length;)!function(){var t=n.shift();if("number"==typeof t){if("array"!==i.type)throw new Error("Cannot get child node at index "+t+": node is no array");i=i.childs[t]}else{if("object"!==i.type)throw new Error("Cannot get child node "+t+": node is no object");i=i.childs.filter(function(e){return e.field===t})[0]}}();return i}},{key:"findParents",value:function(){for(var e=[],t=this.parent;t;)e.unshift(t),t=t.parent;return e}},{key:"setError",value:function(e,t){this.error=e,this.errorChild=t,this.dom&&this.dom.tr&&this.updateError()}},{key:"updateError",value:function(){var r=this,s=this.fieldError||this.valueError||this.error,e=this.dom.tdError;if(s&&this.dom&&this.dom.tr){Object(P.addClassName)(this.dom.tr,"jsoneditor-validation-error"),e||(e=document.createElement("td"),this.dom.tdError=e,this.dom.tdValue.parentNode.appendChild(e));var a=document.createElement("button");a.type="button",a.className="jsoneditor-button jsoneditor-schema-error";var t=function(){r.dom.popupAnchor&&r.dom.popupAnchor.destroy()},l=function(){delete r.dom.popupAnchor},n=function(e){var t=r.editor.frame;r.dom.popupAnchor=Object(g.a)(a,r.editor.getPopupAnchor(),l,e);var n=a.getBoundingClientRect(),i=120<t.getBoundingClientRect().width-n.x?"jsoneditor-above":"jsoneditor-left",o=document.createElement("div");o.className="jsoneditor-popover "+i,o.appendChild(document.createTextNode(s.message)),r.dom.popupAnchor.appendChild(o)};a.onmouseover=function(){r.dom.popupAnchor||n(!0)},a.onfocus=function(){t(),n(!1)},a.onblur=function(){t()};var i=this.errorChild;for(i&&(a.onclick=function(){i.findParents().forEach(function(e){e.expand(!1)}),i.scrollTo(function(){i.focus()})});e.firstChild;)e.removeChild(e.firstChild);e.appendChild(a)}else this.dom.tr&&Object(P.removeClassName)(this.dom.tr,"jsoneditor-validation-error"),e&&(this.dom.tdError.parentNode.removeChild(this.dom.tdError),delete this.dom.tdError)}},{key:"getIndex",value:function(){if(this.parent){var e=this.parent.childs.indexOf(this);return-1!==e?e:null}return-1}},{key:"setParent",value:function(e){this.parent=e}},{key:"setField",value:function(e,t){this.field=e,this.previousField=e,this.fieldEditable=!0===t}},{key:"getField",value:function(){return void 0===this.field&&this._getDomField(),this.field}},{key:"setValue",value:function(e,t){var n,i,o,r,s,a,l,c,d=this.childs;if(this.type=this._getType(e),t&&t!==this.type){if("string"!==t||"auto"!==this.type)throw new Error('Type mismatch: cannot cast value of type "'+this.type+' to the specified type "'+t+'"');this.type=t}if("array"===this.type){for(this.childs||(this.childs=[]),i=0;i<e.length;i++){void 0===(s=e[i])||s instanceof Function||(i<this.childs.length?((n=this.childs[i]).fieldEditable=!1,n.index=i,n.setValue(s)):(n=new A(this.editor,{value:s}),r=i<this.getMaxVisibleChilds(),this.appendChild(n,r,!1)))}for(o=this.childs.length;o>=e.length;o--)this.removeChild(this.childs[o],!1)}else if("object"===this.type){for(this.childs||(this.childs=[]),o=this.childs.length-1;0<=o;o--)j(e,this.childs[o].field)||this.removeChild(this.childs[o],!1);for(var h in i=0,e){j(e,h)&&(void 0===(s=e[h])||s instanceof Function||((a=this.findChildByProperty(h))?(a.setField(h,!0),a.setValue(s)):(l=new A(this.editor,{field:h,value:s}),c=i<this.getMaxVisibleChilds(),this.appendChild(l,c,!1))),i++)}!(this.value="")===this.editor.options.sortObjectKeys&&this.sort([],"asc",!1)}else this.hideChilds(),delete this.append,delete this.showMore,delete this.expanded,delete this.childs,this.value=e;Array.isArray(d)!==Array.isArray(this.childs)&&this.recreateDom(),this.updateDom({updateIndexes:!0}),this.previousValue=this.value}},{key:"setInternalValue",value:function(e){var t,n,i,o,r,s=this.childs;if(this.type=e.type,"array"===e.type){for(this.childs||(this.childs=[]),o=0;o<e.childs.length;o++)void 0===(t=e.childs[o])||t instanceof Function||(o<this.childs.length?((n=this.childs[o]).fieldEditable=!1,n.index=o,n.setInternalValue(t)):(n=new A(this.editor,{internalValue:t}),i=o<this.getMaxVisibleChilds(),this.appendChild(n,i,!1)));for(r=this.childs.length;r>=e.childs.length;r--)this.removeChild(this.childs[r],!1)}else if("object"===e.type){for(this.childs||(this.childs=[]),o=0;o<e.childs.length;o++)void 0===(t=e.childs[o])||t instanceof Function||(o<this.childs.length?(delete(n=this.childs[o]).index,n.setField(t.field,!0),n.setInternalValue(t.value)):(n=new A(this.editor,{field:t.field,internalValue:t.value}),i=o<this.getMaxVisibleChilds(),this.appendChild(n,i,!1)));for(r=this.childs.length;r>=e.childs.length;r--)this.removeChild(this.childs[r],!1)}else this.hideChilds(),delete this.append,delete this.showMore,delete this.expanded,delete this.childs,this.value=e.value;Array.isArray(s)!==Array.isArray(this.childs)&&this.recreateDom(),this.updateDom({updateIndexes:!0}),this.previousValue=this.value}},{key:"recreateDom",value:function(){var e;this.dom&&this.dom.tr&&this.dom.tr.parentNode?(e=this._detachFromDom(),this.clearDom(),this._attachToDom(e)):this.clearDom()}},{key:"getValue",value:function(){if("array"===this.type){var t=[];return this.childs.forEach(function(e){t.push(e.getValue())}),t}if("object"!==this.type)return void 0===this.value&&this._getDomValue(),this.value;var n={};return this.childs.forEach(function(e){n[e.getField()]=e.getValue()}),n}},{key:"getInternalValue",value:function(){return"array"===this.type?{type:this.type,childs:this.childs.map(function(e){return e.getInternalValue()})}:"object"===this.type?{type:this.type,childs:this.childs.map(function(e){return{field:e.getField(),value:e.getInternalValue()}})}:(void 0===this.value&&this._getDomValue(),{type:this.type,value:this.value})}},{key:"getLevel",value:function(){return this.parent?this.parent.getLevel()+1:0}},{key:"getNodePath",value:function(){var e=this.parent?this.parent.getNodePath():[];return e.push(this),e}},{key:"clone",value:function(){var n,i=new A(this.editor);return i.type=this.type,i.field=this.field,i.fieldInnerText=this.fieldInnerText,i.fieldEditable=this.fieldEditable,i.previousField=this.previousField,i.value=this.value,i.valueInnerText=this.valueInnerText,i.previousValue=this.previousValue,i.expanded=this.expanded,i.visibleChilds=this.visibleChilds,this.childs?(n=[],this.childs.forEach(function(e){var t=e.clone();t.setParent(i),n.push(t)}),i.childs=n):i.childs=void 0,i}},{key:"expand",value:function(t){this.childs&&(this.expanded=!0,this.dom.expand&&(this.dom.expand.className="jsoneditor-button jsoneditor-expanded"),this.showChilds(),!1!==t&&this.childs.forEach(function(e){e.expand(t)}),this.updateDom({recurse:!1}))}},{key:"collapse",value:function(t){this.childs&&(this.hideChilds(),!1!==t&&this.childs.forEach(function(e){e.collapse(t)}),this.dom.expand&&(this.dom.expand.className="jsoneditor-button jsoneditor-collapsed"),this.expanded=!1,this.updateDom({recurse:!1}))}},{key:"showChilds",value:function(){if(this.childs&&this.expanded){var e=this.dom.tr,t=e?e.parentNode:void 0;if(t){var n=this.getAppendDom();n.parentNode||((o=e.nextSibling)?t.insertBefore(n,o):t.appendChild(n));for(var i=Math.min(this.childs.length,this.visibleChilds),o=this._getNextTr(),r=0;r<i;r++){var s=this.childs[r];s.getDom().parentNode||t.insertBefore(s.getDom(),o),s.showChilds()}var a=this.getShowMoreDom();o=this._getNextTr(),a.parentNode||t.insertBefore(a,o),this.showMore.updateDom()}}}},{key:"_getNextTr",value:function(){return this.showMore&&this.showMore.getDom().parentNode?this.showMore.getDom():this.append&&this.append.getDom().parentNode?this.append.getDom():void 0}},{key:"hide",value:function(e){var t=this.dom.tr,n=t?t.parentNode:void 0;n&&n.removeChild(t),this.dom.popupAnchor&&this.dom.popupAnchor.destroy(),this.hideChilds(e)}},{key:"hideChilds",value:function(e){var t,n;this.childs&&this.expanded&&((t=this.getAppendDom()).parentNode&&t.parentNode.removeChild(t),this.childs.forEach(function(e){e.hide()}),(n=this.getShowMoreDom()).parentNode&&n.parentNode.removeChild(n),e&&!e.resetVisibleChilds||(this.visibleChilds=this.getMaxVisibleChilds()))}},{key:"_updateCssClassName",value:function(){var e;this.dom.field&&this.editor&&this.editor.options&&"function"==typeof this.editor.options.onClassName&&this.dom.tree&&(Object(P.removeAllClassNames)(this.dom.tree),e=this.editor.options.onClassName({path:this.getPath(),field:this.field,value:this.value})||"",Object(P.addClassName)(this.dom.tree,"jsoneditor-values "+e))}},{key:"recursivelyUpdateCssClassesOnNodes",value:function(){if(this._updateCssClassName(),Array.isArray(this.childs))for(var e=0;e<this.childs.length;e++)this.childs[e].recursivelyUpdateCssClassesOnNodes()}},{key:"expandTo",value:function(){for(var e=this.parent;e;)e.expanded||e.expand(),e=e.parent}},{key:"appendChild",value:function(e,t,n){var i,o,r;this._hasChilds()&&(e.setParent(this),e.fieldEditable="object"===this.type,"array"===this.type&&(e.index=this.childs.length),"object"===this.type&&void 0===e.field&&e.setField(""),this.childs.push(e),this.expanded&&!1!==t&&(i=e.getDom(),r=(o=this._getNextTr())?o.parentNode:void 0,o&&r&&r.insertBefore(i,o),e.showChilds(),this.visibleChilds++),!1!==n&&(this.updateDom({updateIndexes:!0}),e.updateDom({recurse:!0})))}},{key:"moveBefore",value:function(e,t,n){var i,o,r;this._hasChilds()&&((i=this.dom.tr?this.dom.tr.parentNode:void 0)&&((o=document.createElement("tr")).style.height=i.clientHeight+"px",i.appendChild(o)),e.parent&&e.parent.removeChild(e),t instanceof M||!t?this.childs.length+1>this.visibleChilds?(r=this.childs[this.visibleChilds-1],this.insertBefore(e,r,n)):this.appendChild(e,!0,n):this.insertBefore(e,t,n),i&&i.removeChild(o))}},{key:"insertBefore",value:function(e,t,n){if(this._hasChilds()){if(this.visibleChilds++,"object"===this.type&&void 0===e.field&&e.setField(""),t===this.append)e.setParent(this),e.fieldEditable="object"===this.type,this.childs.push(e);else{var i=this.childs.indexOf(t);if(-1===i)throw new Error("Node not found");e.setParent(this),e.fieldEditable="object"===this.type,this.childs.splice(i,0,e)}var o,r,s;this.expanded&&(o=e.getDom(),s=(r=t.getDom())?r.parentNode:void 0,r&&s&&s.insertBefore(o,r),e.showChilds(),this.showChilds()),!1!==n&&(this.updateDom({updateIndexes:!0}),e.updateDom({recurse:!0}))}}},{key:"insertAfter",value:function(e,t){var n,i;this._hasChilds()&&(n=this.childs.indexOf(t),(i=this.childs[n+1])?this.insertBefore(e,i):this.appendChild(e))}},{key:"search",value:function(t,n){Array.isArray(n)||(n=[]);var e=t?t.toLowerCase():void 0;return delete this.searchField,delete this.searchValue,void 0!==this.field&&n.length<=this.MAX_SEARCH_RESULTS&&(-1!==String(this.field).toLowerCase().indexOf(e)&&(this.searchField=!0,n.push({node:this,elem:"field"})),this._updateDomField()),this._hasChilds()?this.childs&&this.childs.forEach(function(e){e.search(t,n)}):void 0!==this.value&&n.length<=this.MAX_SEARCH_RESULTS&&(-1!==String(this.value).toLowerCase().indexOf(e)&&(this.searchValue=!0,n.push({node:this,elem:"value"})),this._updateDomValue()),n}},{key:"scrollTo",value:function(e){this.expandPathToNode(),this.dom.tr&&this.dom.tr.parentNode&&this.editor.scrollTo(this.dom.tr.offsetTop,e)}},{key:"expandPathToNode",value:function(){for(var e=this;e&&e.parent;){for(var t="array"===e.parent.type?e.index:e.parent.childs.indexOf(e);e.parent.visibleChilds<t+1;)e.parent.visibleChilds+=this.getMaxVisibleChilds();e.parent.expand(!1),e=e.parent}}},{key:"focus",value:function(e){if(A.focusElement=e,this.dom.tr&&this.dom.tr.parentNode){var t=this.dom;switch(e){case"drag":t.drag?t.drag.focus():t.menu.focus();break;case"menu":t.menu.focus();break;case"expand":this._hasChilds()?t.expand.focus():t.field&&this.fieldEditable?(t.field.focus(),Object(P.selectContentEditable)(t.field)):t.value&&!this._hasChilds()?(t.value.focus(),Object(P.selectContentEditable)(t.value)):t.menu.focus();break;case"field":t.field&&this.fieldEditable?(t.field.focus(),Object(P.selectContentEditable)(t.field)):t.value&&!this._hasChilds()?(t.value.focus(),Object(P.selectContentEditable)(t.value)):this._hasChilds()?t.expand.focus():t.menu.focus();break;case"value":default:t.select?t.select.focus():t.value&&!this._hasChilds()?(t.value.focus(),Object(P.selectContentEditable)(t.value)):t.field&&this.fieldEditable?(t.field.focus(),Object(P.selectContentEditable)(t.field)):this._hasChilds()?t.expand.focus():t.menu.focus()}}}},{key:"containsNode",value:function(e){if(this===e)return!0;var t=this.childs;if(t)for(var n=0,i=t.length;n<i;n++)if(t[n].containsNode(e))return!0;return!1}},{key:"removeChild",value:function(e,t){if(this.childs){var n=this.childs.indexOf(e);if(-1!==n){n<this.visibleChilds&&this.expanded&&this.visibleChilds--,e.hide(),delete e.searchField,delete e.searchValue;var i=this.childs.splice(n,1)[0];return i.parent=null,!1!==t&&this.updateDom({updateIndexes:!0}),i}}}},{key:"_remove",value:function(e){this.removeChild(e)}},{key:"changeType",value:function(e){var t,n=this.type;n!==e&&("string"!==e&&"auto"!==e||"string"!==n&&"auto"!==n?(t=this._detachFromDom(),this.clearDom(),"object"===(this.type=e)?(this.childs||(this.childs=[]),this.childs.forEach(function(e){e.clearDom(),delete e.index,e.fieldEditable=!0,void 0===e.field&&(e.field="")}),"string"!==n&&"auto"!==n||(this.expanded=!0)):"array"===e?(this.childs||(this.childs=[]),this.childs.forEach(function(e,t){e.clearDom(),e.fieldEditable=!1,e.index=t}),"string"!==n&&"auto"!==n||(this.expanded=!0)):this.expanded=!1,this._attachToDom(t)):this.type=e,"auto"!==e&&"string"!==e||(this.value="string"===e?String(this.value):Object(P.parseString)(String(this.value)),this.focus()),this.updateDom({updateIndexes:!0}))}},{key:"deepEqual",value:function(e){var t;if("array"===this.type){if(!Array.isArray(e))return!1;if(this.childs.length!==e.length)return!1;for(t=0;t<this.childs.length;t++)if(!this.childs[t].deepEqual(e[t]))return!1}else if("object"===this.type){if("object"!==_(e)||!e)return!1;var n=Object.keys(e);if(this.childs.length!==n.length)return!1;for(t=0;t<n.length;t++){var i=this.childs[t];if(i.field!==n[t]||!i.deepEqual(e[i.field]))return!1}}else if(this.value!==e)return!1;return!0}},{key:"_getDomValue",value:function(){if(this._clearValueError(),this.dom.value&&"array"!==this.type&&"object"!==this.type&&(this.valueInnerText=Object(P.getInnerText)(this.dom.value),""===this.valueInnerText&&""!==this.dom.value.innerHTML&&(this.dom.value.textContent="")),void 0!==this.valueInnerText)try{var e,t;(t="string"===this.type?this._unescapeHTML(this.valueInnerText):(e=this._unescapeHTML(this.valueInnerText),Object(P.parseString)(e)))!==this.value&&(this.value=t,this._debouncedOnChangeValue())}catch(e){this._setValueError(Object(h.c)("cannotParseValueError"))}}},{key:"_setValueError",value:function(e){this.valueError={message:e},this.updateError()}},{key:"_clearValueError",value:function(){this.valueError&&(this.valueError=null,this.updateError())}},{key:"_setFieldError",value:function(e){this.fieldError={message:e},this.updateError()}},{key:"_clearFieldError",value:function(){this.fieldError&&(this.fieldError=null,this.updateError())}},{key:"_onChangeValue",value:function(){var e,t=this.editor.getDomSelection();t.range&&(e=Object(P.textDiff)(String(this.value),String(this.previousValue)),t.range.startOffset=e.start,t.range.endOffset=e.end);var n,i=this.editor.getDomSelection();i.range&&(n=Object(P.textDiff)(String(this.previousValue),String(this.value)),i.range.startOffset=n.start,i.range.endOffset=n.end),this.editor._onAction("editValue",{path:this.getInternalPath(),oldValue:this.previousValue,newValue:this.value,oldSelection:t,newSelection:i}),this.previousValue=this.value}},{key:"_onChangeField",value:function(){var e,t=this.editor.getDomSelection(),n=this.previousField||"";t.range&&(e=Object(P.textDiff)(this.field,n),t.range.startOffset=e.start,t.range.endOffset=e.end);var i,o=this.editor.getDomSelection();o.range&&(i=Object(P.textDiff)(n,this.field),o.range.startOffset=i.start,o.range.endOffset=i.end),this.editor._onAction("editField",{parentPath:this.parent.getInternalPath(),index:this.getIndex(),oldValue:this.previousField,newValue:this.field,oldSelection:t,newSelection:o}),this.previousField=this.field}},{key:"_updateDomValue",value:function(){var e=this.dom.value;if(e){var t,n=["jsoneditor-value"],i=this.value,o="auto"===this.type?Object(P.getType)(i):this.type,r="string"===o&&Object(P.isUrl)(i);if(n.push("jsoneditor-"+o),r&&n.push("jsoneditor-url"),""===String(this.value)&&"array"!==this.type&&"object"!==this.type&&n.push("jsoneditor-empty"),this.searchValueActive&&n.push("jsoneditor-highlight-active"),this.searchValue&&n.push("jsoneditor-highlight"),e.className=n.join(" "),"array"===o||"object"===o?(t=this.childs?this.childs.length:0,e.title=this.type+" containing "+t+" items"):r&&this.editable.value?e.title=Object(h.c)("openUrl"):e.title="","boolean"===o&&this.editable.value?(this.dom.checkbox||(this.dom.checkbox=document.createElement("input"),this.dom.checkbox.type="checkbox",this.dom.tdCheckbox=document.createElement("td"),this.dom.tdCheckbox.className="jsoneditor-tree",this.dom.tdCheckbox.appendChild(this.dom.checkbox),this.dom.tdValue.parentNode.insertBefore(this.dom.tdCheckbox,this.dom.tdValue)),this.dom.checkbox.checked=this.value):this.dom.tdCheckbox&&(this.dom.tdCheckbox.parentNode.removeChild(this.dom.tdCheckbox),delete this.dom.tdCheckbox,delete this.dom.checkbox),this.enum&&this.editable.value){if(!this.dom.select){this.dom.select=document.createElement("select"),this.id=this.field+"_"+(new Date).getUTCMilliseconds(),this.dom.select.id=this.id,this.dom.select.name=this.dom.select.id,this.dom.select.option=document.createElement("option"),this.dom.select.option.value="",this.dom.select.option.textContent="--",this.dom.select.appendChild(this.dom.select.option);for(var s=0;s<this.enum.length;s++)this.dom.select.option=document.createElement("option"),this.dom.select.option.value=this.enum[s],this.dom.select.option.textContent=this.enum[s],this.dom.select.option.value===this.value&&(this.dom.select.option.selected=!0),this.dom.select.appendChild(this.dom.select.option);this.dom.tdSelect=document.createElement("td"),this.dom.tdSelect.className="jsoneditor-tree",this.dom.tdSelect.appendChild(this.dom.select),this.dom.tdValue.parentNode.insertBefore(this.dom.tdSelect,this.dom.tdValue)}!this.schema||j(this.schema,"oneOf")||j(this.schema,"anyOf")||j(this.schema,"allOf")?delete this.valueFieldHTML:(this.valueFieldHTML=this.dom.tdValue.innerHTML,this.dom.tdValue.style.visibility="hidden",this.dom.tdValue.textContent="")}else this.dom.tdSelect&&(this.dom.tdSelect.parentNode.removeChild(this.dom.tdSelect),delete this.dom.tdSelect,delete this.dom.select,this.dom.tdValue.innerHTML=this.valueFieldHTML,this.dom.tdValue.style.visibility="",delete this.valueFieldHTML);if(this.editable.value&&this.editor.options.colorPicker&&"string"==typeof i&&Object(P.isValidColor)(i)?(this.dom.color||(this.dom.color=document.createElement("div"),this.dom.color.className="jsoneditor-color",this.dom.tdColor=document.createElement("td"),this.dom.tdColor.className="jsoneditor-tree",this.dom.tdColor.appendChild(this.dom.color),this.dom.tdValue.parentNode.insertBefore(this.dom.tdColor,this.dom.tdValue)),Object(P.addClassName)(this.dom.value,"jsoneditor-color-value"),this.dom.color.style.backgroundColor=i):this._deleteDomColor(),this._showTimestampTag()){this.dom.date||(this.dom.date=document.createElement("div"),this.dom.date.className="jsoneditor-date",this.dom.value.parentNode.appendChild(this.dom.date));var a=null;if("function"==typeof this.editor.options.timestampFormat&&(a=this.editor.options.timestampFormat({field:this.field,value:this.value,path:this.getPath()})),a){for(;this.dom.date.firstChild;)this.dom.date.removeChild(this.dom.date.firstChild);this.dom.date.appendChild(document.createTextNode(a))}else this.dom.date.textContent=new Date(i).toISOString();this.dom.date.title=new Date(i).toString()}else this.dom.date&&(this.dom.date.parentNode.removeChild(this.dom.date),delete this.dom.date);Object(P.stripFormatting)(e),this._updateDomDefault()}}},{key:"_deleteDomColor",value:function(){this.dom.color&&(this.dom.tdColor.parentNode.removeChild(this.dom.tdColor),delete this.dom.tdColor,delete this.dom.color,Object(P.removeClassName)(this.dom.value,"jsoneditor-color-value"))}},{key:"_updateDomField",value:function(){var e,t=this.dom.field;t&&((e=Object(P.makeFieldTooltip)(this.schema,this.editor.options.language))&&(t.title=e),(""===String(this.field)&&"array"!==this.parent.type?Object(P.addClassName):Object(P.removeClassName))(t,"jsoneditor-empty"),(this.searchFieldActive?Object(P.addClassName):Object(P.removeClassName))(t,"jsoneditor-highlight-active"),(this.searchField?Object(P.addClassName):Object(P.removeClassName))(t,"jsoneditor-highlight"),Object(P.stripFormatting)(t))}},{key:"_getDomField",value:function(e){if(this._clearFieldError(),this.dom.field&&this.fieldEditable&&(this.fieldInnerText=Object(P.getInnerText)(this.dom.field),""===this.fieldInnerText&&""!==this.dom.field.innerHTML&&(this.dom.field.textContent="")),void 0!==this.fieldInnerText)try{var t=this._unescapeHTML(this.fieldInnerText),n=this.parent.getFieldNames(this);-1!==n.indexOf(t)?e?(t=Object(P.findUniqueName)(t,n))!==this.field&&(this.field=t,this._debouncedOnChangeField()):this._setFieldError(Object(h.c)("duplicateFieldError")):t!==this.field&&(this.field=t,this._debouncedOnChangeField())}catch(e){this._setFieldError(Object(h.c)("cannotParseFieldError"))}}},{key:"_updateDomDefault",value:function(){var e
;!this.schema||void 0===this.schema.default||this._hasChilds()||(e=this.dom.select?this.dom.select:this.dom.value)&&(this.value===this.schema.default?(e.title=Object(h.c)("default"),Object(P.addClassName)(e,"jsoneditor-is-default"),Object(P.removeClassName)(e,"jsoneditor-is-not-default")):(e.removeAttribute("title"),Object(P.removeClassName)(e,"jsoneditor-is-default"),Object(P.addClassName)(e,"jsoneditor-is-not-default")))}},{key:"_showTimestampTag",value:function(){if("number"!=typeof this.value)return!1;var e=this.editor.options.timestampTag;if("function"!=typeof e)return!0===e&&Object(P.isTimestamp)(this.field,this.value);var t=e({field:this.field,value:this.value,path:this.getPath()});return"boolean"==typeof t?t:Object(P.isTimestamp)(this.field,this.value)}},{key:"clearDom",value:function(){this.dom={}}},{key:"getDom",value:function(){var e,t,n,i,o=this.dom;if(o.tr)return o.tr;this._updateEditability(),o.tr=document.createElement("tr"),"tree"===(o.tr.node=this).editor.options.mode&&(e=document.createElement("td"),this.editable.field&&this.parent&&((t=document.createElement("button")).type="button",(o.drag=t).className="jsoneditor-button jsoneditor-dragarea",t.title=Object(h.c)("drag"),e.appendChild(t)),o.tr.appendChild(e),n=document.createElement("td"),(i=document.createElement("button")).type="button",(o.menu=i).className="jsoneditor-button jsoneditor-contextmenu-button",i.title=Object(h.c)("actionsMenu"),n.appendChild(o.menu),o.tr.appendChild(n));var r=document.createElement("td");return o.tr.appendChild(r),o.tree=this._createDomTree(),r.appendChild(o.tree),this.updateDom({updateIndexes:!0}),o.tr}},{key:"isVisible",value:function(){return this.dom&&this.dom.tr&&this.dom.tr.parentNode||!1}},{key:"isDescendantOf",value:function(e){for(var t=this.parent;t;){if(t===e)return!0;t=t.parent}return!1}},{key:"_createDomField",value:function(){return document.createElement("div")}},{key:"setHighlight",value:function(t){this.dom.tr&&((t?Object(P.addClassName):Object(P.removeClassName))(this.dom.tr,"jsoneditor-highlight"),this.append&&this.append.setHighlight(t),this.childs&&this.childs.forEach(function(e){e.setHighlight(t)}))}},{key:"setSelected",value:function(t,e){this.selected=t,this.dom.tr&&((t?Object(P.addClassName):Object(P.removeClassName))(this.dom.tr,"jsoneditor-selected"),(e?Object(P.addClassName):Object(P.removeClassName))(this.dom.tr,"jsoneditor-first"),this.append&&this.append.setSelected(t),this.showMore&&this.showMore.setSelected(t),this.childs&&this.childs.forEach(function(e){e.setSelected(t)}))}},{key:"updateValue",value:function(e){this.value=e,this.previousValue=e,this.valueError=void 0,this.updateDom()}},{key:"updateField",value:function(e){this.field=e,this.previousField=e,this.fieldError=void 0,this.updateDom()}},{key:"updateDom",value:function(t){var e=this.dom.tree;e&&(e.style.marginLeft=24*this.getLevel()+"px");var n,i,o,r=this.dom.field;r&&(this.fieldEditable?(r.contentEditable=this.editable.field,r.spellcheck=!1,r.className="jsoneditor-field"):(r.contentEditable=!1,r.className="jsoneditor-readonly"),i=void 0!==this.index?this.index:void 0!==this.field?this.field:(n=this.editor.options.schema?A._findSchema(this.editor.options.schema,this.editor.options.schemaRefs||{},this.getPath()):void 0)&&n.title?n.title:this._hasChilds()?this.type:"",o=this._escapeHTML(i),document.activeElement===r&&o===this._unescapeHTML(Object(P.getInnerText)(r))||(r.innerHTML=o),this._updateSchema());var s,a=this.dom.value;a&&("array"===this.type||"object"===this.type?this.updateNodeName():(s=this._escapeHTML(this.value),document.activeElement===a&&s===this._unescapeHTML(Object(P.getInnerText)(a))||(a.innerHTML=s)));var l=this.dom.tr;l&&("array"===this.type||"object"===this.type?(Object(P.addClassName)(l,"jsoneditor-expandable"),this.expanded?(Object(P.addClassName)(l,"jsoneditor-expanded"),Object(P.removeClassName)(l,"jsoneditor-collapsed")):(Object(P.addClassName)(l,"jsoneditor-collapsed"),Object(P.removeClassName)(l,"jsoneditor-expanded"))):(Object(P.removeClassName)(l,"jsoneditor-expandable"),Object(P.removeClassName)(l,"jsoneditor-expanded"),Object(P.removeClassName)(l,"jsoneditor-collapsed"))),this._updateDomField(),this._updateDomValue(),t&&!0===t.updateIndexes&&this._updateDomIndexes(),t&&!0===t.recurse&&this.childs&&this.childs.forEach(function(e){e.updateDom(t)}),this.error&&this.updateError(),this.append&&this.append.updateDom(),this.showMore&&this.showMore.updateDom(),this._updateCssClassName()}},{key:"_updateSchema",value:function(){this.editor&&this.editor.options&&(this.schema=this.editor.options.schema?A._findSchema(this.editor.options.schema,this.editor.options.schemaRefs||{},this.getPath()):null,this.schema?this.enum=A._findEnum(this.schema):delete this.enum)}},{key:"_updateDomIndexes",value:function(){var e=this.dom.value,t=this.childs;e&&t&&("array"===this.type?t.forEach(function(e,t){e.index=t;var n=e.dom.field;n&&(n.textContent=t)}):"object"===this.type&&t.forEach(function(e){void 0!==e.index&&(delete e.index,void 0===e.field&&(e.field=""))}))}},{key:"_createDomValue",value:function(){var e;return"array"===this.type?(e=document.createElement("div")).textContent="[...]":"object"===this.type?(e=document.createElement("div")).textContent="{...}":(!this.editable.value&&Object(P.isUrl)(this.value)?(e=document.createElement("a")).href=this.value:((e=document.createElement("div")).contentEditable=this.editable.value,e.spellcheck=!1),e.innerHTML=this._escapeHTML(this.value)),e}},{key:"_createDomExpandButton",value:function(){var e=document.createElement("button");return e.type="button",this._hasChilds()?(e.className=this.expanded?"jsoneditor-button jsoneditor-expanded":"jsoneditor-button jsoneditor-collapsed",e.title=Object(h.c)("expandTitle")):(e.className="jsoneditor-button jsoneditor-invisible",e.title=""),e}},{key:"_createDomTree",value:function(){var e=this.dom,t=document.createElement("table"),n=document.createElement("tbody");t.style.borderCollapse="collapse",t.className="jsoneditor-values",t.appendChild(n);var i=document.createElement("tr");n.appendChild(i);var o=document.createElement("td");o.className="jsoneditor-tree",i.appendChild(o),e.expand=this._createDomExpandButton(),o.appendChild(e.expand),e.tdExpand=o;var r=document.createElement("td");r.className="jsoneditor-tree",i.appendChild(r),e.field=this._createDomField(),r.appendChild(e.field),e.tdField=r;var s=document.createElement("td");s.className="jsoneditor-tree",i.appendChild(s),"object"!==this.type&&"array"!==this.type&&(s.appendChild(document.createTextNode(":")),s.className="jsoneditor-separator"),e.tdSeparator=s;var a=document.createElement("td");return a.className="jsoneditor-tree",i.appendChild(a),e.value=this._createDomValue(),a.appendChild(e.value),e.tdValue=a,t}},{key:"onEvent",value:function(e){var t,n,i=e.type,o=e.target||e.srcElement,r=this.dom,s=this,a=this._hasChilds();"function"==typeof this.editor.options.onEvent&&this._onEvent(e),o!==r.drag&&o!==r.menu||("mouseover"===i?this.editor.highlighter.highlight(this):"mouseout"===i&&this.editor.highlighter.unhighlight()),"click"===i&&o===r.menu&&((t=s.editor.highlighter).highlight(s),t.lock(),Object(P.addClassName)(r.menu,"jsoneditor-selected"),this.showContextMenu(r.menu,function(){Object(P.removeClassName)(r.menu,"jsoneditor-selected"),t.unlock(),t.unhighlight()})),"click"===i&&o===r.expand&&a&&(n=e.ctrlKey,this._onExpand(n)),"click"!==i||e.target!==s.dom.tdColor&&e.target!==s.dom.color||this._showColorPicker(),"change"===i&&o===r.checkbox&&(this.dom.value.textContent=String(!this.value),this._getDomValue(),this._updateDomDefault()),"change"===i&&o===r.select&&(this.dom.value.innerHTML=this._escapeHTML(r.select.value),this._getDomValue(),this._updateDomValue());var l=r.value;if(o===l)switch(i){case"blur":case"change":this._getDomValue(),this._clearValueError(),this._updateDomValue();var c=this._escapeHTML(this.value);c!==this._unescapeHTML(Object(P.getInnerText)(l))&&(l.innerHTML=c);break;case"input":this._getDomValue(),this._updateDomValue();break;case"keydown":case"mousedown":this.editor.selection=this.editor.getDomSelection();break;case"click":e.ctrlKey&&this.editable.value&&Object(P.isUrl)(this.value)&&(e.preventDefault(),window.open(this.value,"_blank"));break;case"keyup":this._getDomValue(),this._updateDomValue();break;case"cut":case"paste":setTimeout(function(){s._getDomValue(),s._updateDomValue()},1)}var d=r.field;if(o===d)switch(i){case"blur":this._getDomField(!0),this._updateDomField();var h=this._escapeHTML(this.field);h!==this._unescapeHTML(Object(P.getInnerText)(d))&&(d.innerHTML=h);break;case"input":this._getDomField(),this._updateSchema(),this._updateDomField(),this._updateDomValue();break;case"keydown":case"mousedown":this.editor.selection=this.editor.getDomSelection();break;case"keyup":this._getDomField(),this._updateDomField();break;case"cut":case"paste":setTimeout(function(){s._getDomField(),s._updateDomField()},1)}var u=r.tree;u&&o===u.parentNode&&"click"===i&&!e.hasMoved&&((void 0!==e.offsetX?e.offsetX<24*(this.getLevel()+1):e.pageX<Object(P.getAbsoluteLeft)(r.tdSeparator))||a?d&&(Object(P.setEndOfContentEditable)(d),d.focus()):l&&!this.enum&&(Object(P.setEndOfContentEditable)(l),l.focus())),(o!==r.tdExpand||a)&&o!==r.tdField&&o!==r.tdSeparator||"click"!==i||e.hasMoved||d&&(Object(P.setEndOfContentEditable)(d),d.focus()),"keydown"===i&&this.onKeyDown(e)}},{key:"_onEvent",value:function(e){var t,n=e.target;n!==this.dom.field&&n!==this.dom.value||(t={field:this.getField(),path:this.getPath()},this._hasChilds()||n!==this.dom.value||(t.value=this.getValue()),this.editor.options.onEvent(t,e))}},{key:"onKeyDown",value:function(e){var t,n,i,o,r,s,a,l,c,d,h,u,p,f,m,v,g,y,b,x,C,_,E=e.which||e.keyCode,w=e.target||e.srcElement,T=e.ctrlKey,j=e.shiftKey,S=e.altKey,N=!1,k="tree"===this.editor.options.mode,O=0<this.editor.multiselection.nodes.length?this.editor.multiselection.nodes:[this],I=O[0],D=O[O.length-1];13===E?w===this.dom.value?this.editable.value&&!e.ctrlKey||Object(P.isUrl)(this.value)&&(window.open(this.value,"_blank"),N=!0):w===this.dom.expand&&this._hasChilds()&&(p=e.ctrlKey,this._onExpand(p),w.focus(),N=!0):68===E?T&&k&&(A.onDuplicate(O),N=!0):69===E?T&&(this._onExpand(j),w.focus(),N=!0):77===E&&k?T&&(this.showContextMenu(w),N=!0):46===E&&k?T&&(A.onRemove(O),N=!0):45===E&&k?T&&!j?(this._onInsertBefore(),N=!0):T&&j&&(this._onInsertAfter(),N=!0):35===E?S&&((f=this._lastNode())&&f.focus(A.focusElement||this._getElementName(w)),N=!0):36===E?S&&((m=this._firstNode())&&m.focus(A.focusElement||this._getElementName(w)),N=!0):37===E?S&&!j?((v=this._previousElement(w))&&this.focus(this._getElementName(v)),N=!0):S&&j&&k&&(y=D.expanded?(g=D.getAppendDom())?g.nextSibling:void 0:D.getDom().nextSibling)&&(n=A.getNodeFromTarget(y),i=y.nextSibling,b=A.getNodeFromTarget(i),n&&n instanceof M&&1!==D.parent.childs.length&&b&&b.parent&&(o=this.editor.getDomSelection(),r=(s=I.parent).childs[D.getIndex()+1]||s.append,a=I.getIndex(),l=b.getIndex(),c=s.getInternalPath(),d=b.parent.getInternalPath(),O.forEach(function(e){b.parent.moveBefore(e,b)}),this.focus(A.focusElement||this._getElementName(w)),this.editor._onAction("moveNodes",{count:O.length,fieldNames:O.map(F),oldParentPath:s.getInternalPath(),newParentPath:I.parent.getInternalPath(),oldIndex:r.getIndex(),newIndex:I.getIndex(),oldIndexRedo:a,newIndexRedo:l,oldParentPathRedo:c,newParentPathRedo:d,oldSelection:o,newSelection:this.editor.getDomSelection()}))):38===E?S&&!j?((t=this._previousNode())&&(this.editor.deselect(!0),t.focus(A.focusElement||this._getElementName(w))),N=!0):!S&&T&&j&&k?((t=this._previousNode())&&((u=this.editor.multiselection).start=u.start||this,u.end=t,h=this.editor._findTopLevelNodes(u.start,u.end),this.editor.select(h),t.focus("field")),N=!0):S&&j&&k&&((t=I._previousNode())&&t.parent&&(o=this.editor.getDomSelection(),r=(s=I.parent).childs[D.getIndex()+1]||s.append,a=I.getIndex(),l=t.getIndex(),c=s.getInternalPath(),d=t.parent.getInternalPath(),O.forEach(function(e){t.parent.moveBefore(e,t)}),this.focus(A.focusElement||this._getElementName(w)),this.editor._onAction("moveNodes",{count:O.length,fieldNames:O.map(F),oldParentPath:s.getInternalPath(),newParentPath:I.parent.getInternalPath(),oldIndex:r.getIndex(),newIndex:I.getIndex(),oldIndexRedo:a,newIndexRedo:l,oldParentPathRedo:c,newParentPathRedo:d,oldSelection:o,newSelection:this.editor.getDomSelection()})),N=!0):39===E?S&&!j?((x=this._nextElement(w))&&this.focus(this._getElementName(x)),N=!0):S&&j&&k&&((C=I.getDom().previousSibling)&&(t=A.getNodeFromTarget(C))&&t.parent&&!t.isVisible()&&(o=this.editor.getDomSelection(),r=(s=I.parent).childs[D.getIndex()+1]||s.append,a=I.getIndex(),l=t.getIndex(),c=s.getInternalPath(),d=t.parent.getInternalPath(),O.forEach(function(e){t.parent.moveBefore(e,t)}),this.focus(A.focusElement||this._getElementName(w)),this.editor._onAction("moveNodes",{count:O.length,fieldNames:O.map(F),oldParentPath:s.getInternalPath(),newParentPath:I.parent.getInternalPath(),oldIndex:r.getIndex(),newIndex:I.getIndex(),oldIndexRedo:a,newIndexRedo:l,oldParentPathRedo:c,newParentPathRedo:d,oldSelection:o,newSelection:this.editor.getDomSelection()}))):40===E&&(S&&!j?((n=this._nextNode())&&(this.editor.deselect(!0),n.focus(A.focusElement||this._getElementName(w))),N=!0):!S&&T&&j&&k?((n=this._nextNode())&&((u=this.editor.multiselection).start=u.start||this,u.end=n,h=this.editor._findTopLevelNodes(u.start,u.end),this.editor.select(h),n.focus("field")),N=!0):S&&j&&k&&((n=D.expanded?D.append?D.append._nextNode():void 0:D._nextNode())&&!n.isVisible()&&(n=n.parent.showMore),n&&n instanceof M&&(n=D),(_=n&&(n._nextNode()||n.parent.append))&&_.parent&&(o=this.editor.getDomSelection(),r=(s=I.parent).childs[D.getIndex()+1]||s.append,a=I.getIndex(),l=_.getIndex(),c=s.getInternalPath(),d=_.parent.getInternalPath(),O.forEach(function(e){_.parent.moveBefore(e,_)}),this.focus(A.focusElement||this._getElementName(w)),this.editor._onAction("moveNodes",{count:O.length,fieldNames:O.map(F),oldParentPath:s.getInternalPath(),newParentPath:I.parent.getInternalPath(),oldParentPathRedo:c,newParentPathRedo:d,oldIndexRedo:a,newIndexRedo:l,oldIndex:r.getIndex(),newIndex:I.getIndex(),oldSelection:o,newSelection:this.editor.getDomSelection()})),N=!0)),N&&(e.preventDefault(),e.stopPropagation())}},{key:"_onExpand",value:function(e){var t,n,i;e&&(i=(n=(t=this.dom.tr.parentNode).parentNode).scrollTop,n.removeChild(t)),this.expanded?this.collapse(e):this.expand(e),e&&(n.appendChild(t),n.scrollTop=i)}},{key:"_showColorPicker",value:function(){var t,e;"function"==typeof this.editor.options.onColorPicker&&this.dom.color&&((t=this)._deleteDomColor(),t.updateDom(),e=Object(g.a)(this.dom.color,this.editor.getPopupAnchor()),this.editor.options.onColorPicker(e,this.value,function(e){"string"==typeof e&&e!==t.value&&(t._deleteDomColor(),t.value=e,t.updateDom(),t._debouncedOnChangeValue())}))}},{key:"getFieldNames",value:function(t){return"object"===this.type?this.childs.filter(function(e){return e!==t}).map(function(e){return e.field}):[]}},{key:"_onInsertBefore",value:function(e,t,n){var i=this.editor.getDomSelection(),o=new A(this.editor,{field:void 0!==e?e:"",value:void 0!==t?t:"",type:n});o.expand(!0);var r=this.getInternalPath();this.parent.insertBefore(o,this),this.editor.highlighter.unhighlight(),o.focus("field");var s=this.editor.getDomSelection();this.editor._onAction("insertBeforeNodes",{nodes:[o],paths:[o.getInternalPath()],beforePath:r,parentPath:this.parent.getInternalPath(),oldSelection:i,newSelection:s})}},{key:"_onInsertAfter",value:function(e,t,n){var i=this.editor.getDomSelection(),o=new A(this.editor,{field:void 0!==e?e:"",value:void 0!==t?t:"",type:n});o.expand(!0),this.parent.insertAfter(o,this),this.editor.highlighter.unhighlight(),o.focus("field");var r=this.editor.getDomSelection();this.editor._onAction("insertAfterNodes",{nodes:[o],paths:[o.getInternalPath()],afterPath:this.getInternalPath(),parentPath:this.parent.getInternalPath(),oldSelection:i,newSelection:r})}},{key:"_onAppend",value:function(e,t,n){var i=this.editor.getDomSelection(),o=new A(this.editor,{field:void 0!==e?e:"",value:void 0!==t?t:"",type:n});o.expand(!0),this.parent.appendChild(o),this.editor.highlighter.unhighlight(),o.focus("field");var r=this.editor.getDomSelection();this.editor._onAction("appendNodes",{nodes:[o],paths:[o.getInternalPath()],parentPath:this.parent.getInternalPath(),oldSelection:i,newSelection:r})}},{key:"_onChangeType",value:function(e){var t,n,i=this.type;e!==i&&(t=this.editor.getDomSelection(),this.changeType(e),n=this.editor.getDomSelection(),this.editor._onAction("changeType",{path:this.getInternalPath(),oldType:i,newType:e,oldSelection:t,newSelection:n}))}},{key:"sort",value:function(s,e,t){var n,a,i=!(2<arguments.length&&void 0!==t)||t;"string"==typeof s&&(s=Object(P.parsePath)(s)),this._hasChilds()&&(this.hideChilds(),n=this.childs,this.childs=this.childs.concat(),a="desc"===e?-1:1,"object"===this.type?this.childs.sort(function(e,t){return a*v()(e.field,t.field)}):this.childs.sort(function(e,t){var n=e.getNestedChild(s),i=t.getNestedChild(s);if(!n)return a;if(!i)return-a;var o=n.value,r=i.value;return"string"!=typeof o&&"string"!=typeof r?r<o?a:o<r?-a:0:a*v()(o,r)}),this._updateDomIndexes(),this.showChilds(),!0===i&&this.editor._onAction("sort",{path:this.getInternalPath(),oldChilds:n,newChilds:this.childs}))}},{key:"update",value:function(e){var t=this.getInternalValue();this.setValue(e),this.editor._onAction("transform",{path:this.getInternalPath(),oldValue:t,newValue:this.getInternalValue()})}},{key:"_detachFromDom",value:function(){var e=this.dom.tr?this.dom.tr.parentNode:void 0,t=this.expanded?this.getAppendDom():this.getDom(),n=t&&t.parentNode?t.nextSibling:void 0;return this.hide({resetVisibleChilds:!1}),{table:e,nextTr:n}}},{key:"_attachToDom",value:function(e){e.table&&(e.nextTr?e.table.insertBefore(this.getDom(),e.nextTr):e.table.appendChild(this.getDom())),this.expanded&&this.showChilds()}},{key:"transform",value:function(e){if(this._hasChilds()){this.hideChilds();try{var t=this.getInternalValue(),n=this.getValue(),i=this.editor.options.executeQuery(n,e);this.setValue(i);var o=this.getInternalValue();this.editor._onAction("transform",{path:this.getInternalPath(),oldValue:t,newValue:o}),this.showChilds()}catch(e){this.showChilds(),this.editor._onError(e)}}}},{key:"extract",value:function(){this.editor.node.hideChilds(),this.hideChilds();try{var e=this.editor.node.getInternalValue();this.editor._setRoot(this);var t=this.editor.node.getInternalValue();this.editor._onAction("transform",{path:this.editor.node.getInternalPath(),oldValue:e,newValue:t})}catch(e){this.editor._onError(e)}finally{this.updateDom({recurse:!0}),this.showChilds()}}},{key:"getNestedChild",value:function(e){for(var t=0,n=this;n&&t<e.length;)n=n.findChildByProperty(e[t]),t++;return n}},{key:"findChildByProperty",value:function(t){if("object"===this.type)return this.childs.find(function(e){return e.field===t})}},{key:"getAppendDom",value:function(){return this.append||(this.append=new M(this.editor),this.append.setParent(this)),this.append.getDom()}},{key:"getShowMoreDom",value:function(){return this.showMore||(this.showMore=new k(this.editor,this)),this.showMore.getDom()}},{key:"nextSibling",value:function(){var e=this.parent.childs.indexOf(this);return this.parent.childs[e+1]||this.parent.append}},{key:"_previousNode",value:function(){var e=null,t=this.getDom();if(t&&t.parentNode)for(var n=t;n=n.previousSibling,e=A.getNodeFromTarget(n),n&&e&&e instanceof M&&!e.isVisible(););return e}},{key:"_nextNode",value:function(){var e=null,t=this.getDom();if(t&&t.parentNode)for(var n=t;n=n.nextSibling,e=A.getNodeFromTarget(n),n&&e&&e instanceof M&&!e.isVisible(););return e}},{key:"_firstNode",value:function(){var e,t=null,n=this.getDom();return n&&n.parentNode&&(e=n.parentNode.firstChild,t=A.getNodeFromTarget(e)),t}},{key:"_lastNode",value:function(){var e=null,t=this.getDom();if(t&&t.parentNode)for(var n=t.parentNode.lastChild,e=A.getNodeFromTarget(n);n&&e&&!e.isVisible();)n=n.previousSibling,e=A.getNodeFromTarget(n);return e}},{key:"_previousElement",value:function(e){var t=this.dom;switch(e){case t.value:if(this.fieldEditable)return t.field;case t.field:if(this._hasChilds())return t.expand;case t.expand:return t.menu;case t.menu:if(t.drag)return t.drag;default:return null}}},{key:"_nextElement",value:function(e){var t=this.dom;switch(e){case t.drag:return t.menu;case t.menu:if(this._hasChilds())return t.expand;case t.expand:if(this.fieldEditable)return t.field;case t.field:if(!this._hasChilds())return t.value;default:return null}}},{key:"_getElementName",value:function(t){var n=this;return Object.keys(this.dom).find(function(e){return n.dom[e]===t})}},{key:"_hasChilds",value:function(){return"array"===this.type||"object"===this.type}},{key:"addTemplates",value:function(t,n){var i=this,e=i.editor.options.templates;null!=e&&(e.length&&t.push({type:"separator"}),e.forEach(function(e){t.push({text:e.text,className:e.className||"jsoneditor-type-object",title:e.title,click:n?function(e,t){i._onAppend(e,t)}.bind(this,e.field,e.value):function(e,t){i._onInsertBefore(e,t)}.bind(this,e.field,e.value)})}))}},{key:"showContextMenu",value:function(e,t){var n,i,o,r,s=this,a=[];this.editable.value&&a.push({text:Object(h.c)("type"),title:Object(h.c)("typeTitle"),className:"jsoneditor-type-"+this.type,submenu:[{text:Object(h.c)("auto"),className:"jsoneditor-type-auto"+("auto"===this.type?" jsoneditor-selected":""),title:Object(h.c)("autoType"),click:function(){s._onChangeType("auto")}},{text:Object(h.c)("array"),className:"jsoneditor-type-array"+("array"===this.type?" jsoneditor-selected":""),title:Object(h.c)("arrayType"),click:function(){s._onChangeType("array")}},{text:Object(h.c)("object"),className:"jsoneditor-type-object"+("object"===this.type?" jsoneditor-selected":""),title:Object(h.c)("objectType"),click:function(){s._onChangeType("object")}},{text:Object(h.c)("string"),className:"jsoneditor-type-string"+("string"===this.type?" jsoneditor-selected":""),title:Object(h.c)("stringType"),click:function(){s._onChangeType("string")}}]}),this._hasChilds()&&(this.editor.options.enableSort&&a.push({text:Object(h.c)("sort"),title:Object(h.c)("sortTitle",{type:this.type}),className:"jsoneditor-sort-asc",click:function(){s.showSortModal()}}),this.editor.options.enableTransform&&a.push({text:Object(h.c)("transform"),title:Object(h.c)("transformTitle",{type:this.type}),className:"jsoneditor-transform",click:function(){s.showTransformModal()}}),this.parent&&a.push({text:Object(h.c)("extract"),title:Object(h.c)("extractTitle",{type:this.type}),className:"jsoneditor-extract",click:function(){s.extract()}})),this.parent&&this.parent._hasChilds()&&(a.length&&a.push({type:"separator"}),n=s.parent.childs,s===n[n.length-1]&&(i=[{text:Object(h.c)("auto"),className:"jsoneditor-type-auto",title:Object(h.c)("autoType"),click:function(){s._onAppend("","","auto")}},{text:Object(h.c)("array"),className:"jsoneditor-type-array",title:Object(h.c)("arrayType"),click:function(){s._onAppend("",[])}},{text:Object(h.c)("object"),className:"jsoneditor-type-object",title:Object(h.c)("objectType"),click:function(){s._onAppend("",{})}},{text:Object(h.c)("string"),className:"jsoneditor-type-string",title:Object(h.c)("stringType"),click:function(){s._onAppend("","","string")}}],s.addTemplates(i,!0),a.push({text:Object(h.c)("appendText"),title:Object(h.c)("appendTitle"),submenuTitle:Object(h.c)("appendSubmenuTitle"),className:"jsoneditor-append",click:function(){s._onAppend("","","auto")},submenu:i})),o=[{text:Object(h.c)("auto"),className:"jsoneditor-type-auto",title:Object(h.c)("autoType"),click:function(){s._onInsertBefore("","","auto")}},{text:Object(h.c)("array"),className:"jsoneditor-type-array",title:Object(h.c)("arrayType"),click:function(){s._onInsertBefore("",[])}},{text:Object(h.c)("object"),className:"jsoneditor-type-object",title:Object(h.c)("objectType"),click:function(){s._onInsertBefore("",{})}},{text:Object(h.c)("string"),className:"jsoneditor-type-string",title:Object(h.c)("stringType"),click:function(){s._onInsertBefore("","","string")}}],s.addTemplates(o,!1),a.push({text:Object(h.c)("insert"),title:Object(h.c)("insertTitle"),submenuTitle:Object(h.c)("insertSub"),className:"jsoneditor-insert",click:function(){s._onInsertBefore("","","auto")},submenu:o}),this.editable.field&&(a.push({text:Object(h.c)("duplicateText"),title:Object(h.c)("duplicateField"),className:"jsoneditor-duplicate",click:function(){A.onDuplicate(s)}}),a.push({text:Object(h.c)("removeText"),title:Object(h.c)("removeField"),className:"jsoneditor-remove",click:function(){A.onRemove(s)}}))),this.editor.options.onCreateMenu&&(r=s.getPath(),a=this.editor.options.onCreateMenu(a,{type:"single",path:r,paths:[r]})),new u.a(a,{close:t}).show(e,this.editor.getPopupAnchor())}},{key:"showSortModal",value:function(){var i=this,e=this.editor.options.modalAnchor||x.a,t=this.getValue();Object(y.showSortModal)(e,t,function(e){var t=e.path,n=Object(P.parsePath)(t);i.sortedBy=e,i.sort(n,e.direction)},i.sortedBy)}},{key:"showTransformModal",value:function(){var t=this,e=this.editor.options,n=e.modalAnchor,i=e.createQuery,o=e.executeQuery,r=e.queryDescription,s=this.getValue();Object(b.showTransformModal)({anchor:n||x.a,json:s,queryDescription:r,createQuery:i,executeQuery:o,onTransform:function(e){t.transform(e)}})}},{key:"_getType",value:function(e){return e instanceof Array?"array":e instanceof Object?"object":"string"==typeof e&&"string"!=typeof Object(P.parseString)(e)?"string":"auto"}},{key:"_escapeHTML",value:function(e){if("string"!=typeof e)return String(e);var t=String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/ {2}/g," &nbsp;").replace(/^ /,"&nbsp;").replace(/ $/,"&nbsp;"),n=JSON.stringify(t),i=n.substring(1,n.length-1);return!0===this.editor.options.escapeUnicode&&(i=Object(P.escapeUnicodeChars)(i)),i}},{key:"_unescapeHTML",value:function(e){var t='"'+this._escapeJSON(e)+'"';return Object(P.parse)(t).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&nbsp;|\u00A0/g," ").replace(/&amp;/g,"&")}},{key:"_escapeJSON",value:function(e){for(var t="",n=0;n<e.length;){var i=e.charAt(n);"\n"===i?t+="\\n":"\\"===i?(t+=i,n++,""!==(i=e.charAt(n))&&-1!=='"\\/bfnrtu'.indexOf(i)||(t+="\\"),t+=i):t+='"'===i?'\\"':i,n++}return t}},{key:"updateNodeName",value:function(){var e,t=this.childs?this.childs.length:0;if("object"===this.type||"array"===this.type){if(this.editor.options.onNodeName)try{e=this.editor.options.onNodeName({path:this.getPath(),size:t,type:this.type})}catch(e){console.error("Error in onNodeName callback: ",e)}this.dom.value.textContent="object"===this.type?"{"+(e||t)+"}":"["+(e||t)+"]"}}},{key:"recursivelyUpdateNodeName",value:function(){var e;if(this.expanded&&(this.updateNodeName(),"undefined"!==this.childs))for(e in this.childs)this.childs[e].recursivelyUpdateNodeName()}}])&&E(e.prototype,t),n&&E(e,n),A}();S.prototype.DEBOUNCE_INTERVAL=150,S.prototype.MAX_SEARCH_RESULTS=999;var w=100;function T(e){return e.getInternalPath()}function F(e){return e.getField()}function j(e,t){return Object.prototype.hasOwnProperty.call(e,t)}S.focusElement=void 0,S.select=function(e){setTimeout(function(){Object(P.selectContentEditable)(e)},0)},S.onDragStart=function(t,e){if(!Array.isArray(t))return S.onDragStart([t],e);var n,i,o,r,s,a;0!==t.length&&(n=t[0],i=t[t.length-1],o=n.parent,r=S.getNodeFromTarget(e.target),s=n.editor,a=Object(P.getAbsoluteTop)(r.dom.tr)-Object(P.getAbsoluteTop)(n.dom.tr),s.mousemove||(s.mousemove=Object(P.addEventListener)(window,"mousemove",function(e){S.onDrag(t,e)})),s.mouseup||(s.mouseup=Object(P.addEventListener)(window,"mouseup",function(e){S.onDragEnd(t,e)})),s.highlighter.lock(),s.drag={oldCursor:document.body.style.cursor,oldSelection:s.getDomSelection(),oldPaths:t.map(T),oldParent:o,oldNextNode:o.childs[i.getIndex()+1]||o.append,oldParentPathRedo:o.getInternalPath(),oldIndexRedo:n.getIndex(),mouseX:e.pageX,offsetY:a,level:n.getLevel()},document.body.style.cursor="move",e.preventDefault())},S.onDrag=function(e,t){if(!Array.isArray(e))return S.onDrag([e],t);if(0!==e.length){var n,i,o,r,s,a,l,c=e[0].editor,d=t.pageY-c.drag.offsetY,h=t.pageX,u=!1,p=e[0],f=p.dom.tr,m=Object(P.getAbsoluteTop)(f),v=f.offsetHeight;if(d<m){for(w=f;w=w.previousSibling,T=S.getNodeFromTarget(w),r=w?Object(P.getAbsoluteTop)(w):0,w&&d<r;);T&&!T.parent&&(T=void 0),T||(w=(i=f.parentNode.firstChild)?i.nextSibling:void 0,(T=S.getNodeFromTarget(w))===p&&(T=void 0)),T&&T.isVisible()&&(r=(w=T.dom.tr)?Object(P.getAbsoluteTop)(w):0)+v<d&&(T=void 0),!T||!1!==c.options.limitDragging&&T.parent!==e[0].parent||(e.forEach(function(e){T.parent.moveBefore(e,T)}),u=!0)}else{var g,y,b=e[e.length-1];if(y=(g=b.expanded&&b.append?b.append.getDom():b.dom.tr)?g.nextSibling:void 0){for(s=Object(P.getAbsoluteTop)(y),n=y;o=S.getNodeFromTarget(n),n&&(a=n.nextSibling?Object(P.getAbsoluteTop)(n.nextSibling):0,l=n?a-s:0,o&&o.parent.childs.length===e.length&&o.parent.childs[e.length-1]===b&&(m+=27),n=n.nextSibling),n&&m+l<d;);if(o&&o.parent){for(var x=h-c.drag.mouseX,C=Math.round(x/24/2),_=c.drag.level+C,E=o.getLevel(),w=o.dom.tr&&o.dom.tr.previousSibling;E<_&&w;){var T=S.getNodeFromTarget(w);if(!e.some(function(e){return e===T||T.isDescendantOf(e)})){if(!(T instanceof M))break;var j=T.parent.childs;if(j.length===e.length&&j[e.length-1]===b)break;E=(o=S.getNodeFromTarget(w)).getLevel()}w=w.previousSibling}o instanceof M&&!o.isVisible()&&o.parent.showMore.isVisible()&&(o=o._nextNode()),o&&(!1===c.options.limitDragging||o.parent===e[0].parent)&&o.dom.tr&&o.dom.tr!==g.nextSibling&&(e.forEach(function(e){o.parent.moveBefore(e,o)}),u=!0)}}}u&&(c.drag.mouseX=h,c.drag.level=p.getLevel()),c.startAutoScroll(d),t.preventDefault()}},S.onDragEnd=function(e,t){if(!Array.isArray(e))return S.onDrag([e],t);var n,i,o,r,s,a,l,c,d,h;0!==e.length&&(n=e[0],i=n.editor,e[0]&&e[0].dom.menu.focus(),o=i.drag.oldParent.getInternalPath(),r=n.parent.getInternalPath(),s=i.drag.oldParent===n.parent,a=i.drag.oldNextNode.getIndex(),l=n.getIndex(),c=i.drag.oldParentPathRedo,d=i.drag.oldIndexRedo,h=s&&d<l?l+e.length:l,s&&d===l||i._onAction("moveNodes",{count:e.length,fieldNames:e.map(F),oldParentPath:o,newParentPath:r,oldIndex:a,newIndex:l,oldIndexRedo:d,newIndexRedo:h,oldParentPathRedo:c,newParentPathRedo:null,oldSelection:i.drag.oldSelection,newSelection:i.getDomSelection()}),document.body.style.cursor=i.drag.oldCursor,i.highlighter.unlock(),e.forEach(function(e){e.updateDom(),t.target!==e.dom.drag&&t.target!==e.dom.menu&&i.highlighter.unhighlight()}),delete i.drag,i.mousemove&&(Object(P.removeEventListener)(window,"mousemove",i.mousemove),delete i.mousemove),i.mouseup&&(Object(P.removeEventListener)(window,"mouseup",i.mouseup),delete i.mouseup),i.stopAutoScroll(),t.preventDefault())},S._findEnum=function(e){if(e.enum)return e.enum;var t=e.oneOf||e.anyOf||e.allOf;if(t){var n=t.filter(function(e){return e.enum});if(0<n.length)return n[0].enum}return null},S._findSchema=function(e,t,n){for(var i=e,o=i,r=(r=e.oneOf||e.anyOf||e.allOf)||[e],s=0;s<r.length;s++){"$ref"in(i=r[s])&&"string"==typeof i.$ref&&(i=t[i.$ref])&&(o=S._findSchema(i,t,n));for(var a=0;a<n.length&&i;a++){var l=n.slice(a+1,n.length),c=n[a];if("string"!=typeof c||!i.patternProperties||i.properties&&c in i.properties)"string"==typeof c&&i.properties?c in i.properties?(i=i.properties[c])&&(o=S._findSchema(i,t,l)):o=null:"number"==typeof c&&i.items&&(i=i.items)&&(o=S._findSchema(i,t,l));else for(var d in i.patternProperties)c.match(d)&&(o=S._findSchema(i.patternProperties[d],t,l))}}return o===e&&0<n.length?null:o},S.onRemove=function(e){if(!Array.isArray(e))return S.onRemove([e]);var t,n,i,o,r,s,a;e&&0<e.length&&(n=(t=e[0]).parent,i=t.editor,o=t.getIndex(),i.highlighter.unhighlight(),r=i.getDomSelection(),S.blurNodes(e),s=i.getDomSelection(),a=e.map(T),e.forEach(function(e){e.parent._remove(e)}),i._onAction("removeNodes",{nodes:e,paths:a,parentPath:n.getInternalPath(),index:o,oldSelection:r,newSelection:s}))},S.onDuplicate=function(e){if(!Array.isArray(e))return S.onDuplicate([e]);var t,i,n,o,r,s,a;e&&0<e.length&&(t=e[e.length-1],i=t.parent,(n=t.editor).deselect(n.multiselection.nodes),o=n.getDomSelection(),r=t,s=e.map(function(e){var t,n=e.clone();return"object"===e.parent.type&&(t=e.parent.getFieldNames(),n.field=Object(P.findUniqueName)(e.field,t)),i.insertAfter(n,r),r=n}),1===e.length?"object"===s[0].parent.type?(s[0].dom.field.innerHTML=C._escapeHTML(e[0].field),s[0].focus("field")):s[0].focus():n.select(s),a=n.getDomSelection(),n._onAction("duplicateNodes",{paths:e.map(T),clonePaths:s.map(T),afterPath:t.getInternalPath(),parentPath:i.getInternalPath(),oldSelection:o,newSelection:a}))},S.getNodeFromTarget=function(e){for(;e;){if(e.node)return e.node;e=e.parentNode}},S.targetIsColorPicker=function(e){var t=S.getNodeFromTarget(e);if(t)for(var n=e&&e.parentNode;n;){if(n===t.dom.color)return!0;n=n.parentNode}return!1},S.blurNodes=function(e){var t,n,i;Array.isArray(e)?(n=(t=e[0]).parent,i=t.getIndex(),n.childs[i+e.length]?n.childs[i+e.length].focus():n.childs[i-1]?n.childs[i-1].focus():n.focus()):S.blurNodes([e])};var M=((N.prototype=new S).getDom=function(){var e=this.dom;if(e.tr)return e.tr;this._updateEditability();var t,n,i=document.createElement("tr");i.className="jsoneditor-append",i.node=this,e.tr=i,"tree"===this.editor.options.mode&&(e.tdDrag=document.createElement("td"),t=document.createElement("td"),e.tdMenu=t,(n=document.createElement("button")).type="button",n.className="jsoneditor-button jsoneditor-contextmenu-button",n.title="Click to open the actions menu (Ctrl+M)",e.menu=n,t.appendChild(e.menu));var o=document.createElement("td"),r=document.createElement("div");return r.appendChild(document.createTextNode("("+Object(h.c)("empty")+")")),r.className="jsoneditor-readonly",o.appendChild(r),e.td=o,e.text=r,this.updateDom(),i},N.prototype.getPath=function(){return null},N.prototype.getIndex=function(){return null},N.prototype.updateDom=function(e){var t=this.dom,n=t.td;n&&(n.style.paddingLeft=24*this.getLevel()+26+"px");var i=t.text;i&&(i.firstChild.nodeValue="("+Object(h.c)("empty")+" "+this.parent.type+")");var o=t.tr;this.isVisible()?t.tr.firstChild||(t.tdDrag&&o.appendChild(t.tdDrag),t.tdMenu&&o.appendChild(t.tdMenu),o.appendChild(n)):t.tr.firstChild&&(t.tdDrag&&o.removeChild(t.tdDrag),t.tdMenu&&o.removeChild(t.tdMenu),o.removeChild(n))},N.prototype.isVisible=function(){return 0===this.parent.childs.length},N.prototype.showContextMenu=function(e,t){var n=this,i=[{text:Object(h.c)("auto"),className:"jsoneditor-type-auto",title:Object(h.c)("autoType"),click:function(){n._onAppend("","","auto")}},{text:Object(h.c)("array"),className:"jsoneditor-type-array",title:Object(h.c)("arrayType"),click:function(){n._onAppend("",[])}},{text:Object(h.c)("object"),className:"jsoneditor-type-object",title:Object(h.c)("objectType"),click:function(){n._onAppend("",{})}},{text:Object(h.c)("string"),className:"jsoneditor-type-string",title:Object(h.c)("stringType"),click:function(){n._onAppend("","","string")}}];n.addTemplates(i,!0);var o,r=[{text:Object(h.c)("appendText"),title:Object(h.c)("appendTitleAuto"),submenuTitle:Object(h.c)("appendSubmenuTitle"),className:"jsoneditor-insert",click:function(){n._onAppend("","","auto")},submenu:i}];this.editor.options.onCreateMenu&&(o=n.parent.getPath(),r=this.editor.options.onCreateMenu(r,{type:"append",path:o,paths:[o]})),new u.a(r,{close:t}).show(e,this.editor.getPopupAnchor())},N.prototype.onEvent=function(e){var t,n=e.type,i=e.target||e.srcElement,o=this.dom;i===o.menu&&("mouseover"===n?this.editor.highlighter.highlight(this.parent):"mouseout"===n&&this.editor.highlighter.unhighlight()),"click"===n&&i===o.menu&&((t=this.editor.highlighter).highlight(this.parent),t.lock(),Object(P.addClassName)(o.menu,"jsoneditor-selected"),this.showContextMenu(o.menu,function(){Object(P.removeClassName)(o.menu,"jsoneditor-selected"),t.unlock(),t.unhighlight()})),"keydown"===n&&this.onKeyDown(e)},N);function N(e){this.editor=e,this.dom={}}var k=((O.prototype=new S).getDom=function(){return this.dom.tr||(this._updateEditability(),this.dom.tr||(n=(t=this).parent,(e=document.createElement("a")).appendChild(document.createTextNode(Object(h.c)("showMore"))),e.href="#",e.onclick=function(e){return n.visibleChilds=Math.floor(n.visibleChilds/n.getMaxVisibleChilds()+1)*n.getMaxVisibleChilds(),t.updateDom(),n.showChilds(),e.preventDefault(),!1},(i=document.createElement("a")).appendChild(document.createTextNode(Object(h.c)("showAll"))),i.href="#",i.onclick=function(e){return n.visibleChilds=1/0,t.updateDom(),n.showChilds(),e.preventDefault(),!1},o=document.createElement("div"),r=document.createTextNode(this._getShowMoreText()),o.className="jsoneditor-show-more",o.appendChild(r),o.appendChild(e),o.appendChild(document.createTextNode(". ")),o.appendChild(i),o.appendChild(document.createTextNode(". ")),(s=document.createElement("td")).appendChild(o),a=document.createElement("tr"),"tree"===this.editor.options.mode&&(a.appendChild(document.createElement("td")),a.appendChild(document.createElement("td"))),a.appendChild(s),a.className="jsoneditor-show-more",this.dom.tr=a,this.dom.moreContents=o,this.dom.moreText=r),this.updateDom()),this.dom.tr;var t,n,e,i,o,r,s,a},O.prototype.updateDom=function(e){var t;this.isVisible()?(this.dom.tr.node=this.parent.childs[this.parent.visibleChilds],this.dom.tr.parentNode||(t=this.parent._getNextTr())&&t.parentNode.insertBefore(this.dom.tr,t),this.dom.moreText.nodeValue=this._getShowMoreText(),this.dom.moreContents.style.marginLeft=24*(this.getLevel()+1)+"px"):this.dom.tr&&this.dom.tr.parentNode&&this.dom.tr.parentNode.removeChild(this.dom.tr)},O.prototype._getShowMoreText=function(){return Object(h.c)("showMoreStatus",{visibleChilds:this.parent.visibleChilds,totalChilds:this.parent.childs.length})+" "},O.prototype.isVisible=function(){return this.parent.expanded&&this.parent.childs.length>this.parent.visibleChilds},O.prototype.onEvent=function(e){"keydown"===e.type&&this.onKeyDown(e)},O);function O(e,t){this.editor=e,this.parent=t,this.dom={}}var I=n(7),D=n(8),A={start:function(e,t){return 0===t.indexOf(e)},contain:function(e,t){return-1<t.indexOf(e)}};var V=n(4),B={};B.create=function(e,t){if(!e)throw new Error("No container element provided.");this.container=e,this.dom={},this.highlighter=new s,this.selection=void 0,this.multiselection={nodes:[]},this.validateSchema=null,this.validationSequence=0,this.errorNodes=[],this.lastSchemaErrors=void 0,this.node=null,this.focusTarget=null,this._setOptions(t),t.autocomplete&&(this.autocomplete=function(l){(l=l||{}).filter=l.filter||"start",l.trigger=l.trigger||"keydown",l.confirmKeys=l.confirmKeys||[39,35,9],l.caseSensitive=l.caseSensitive||!1;var o="",r="",s=document.createElement("div");s.style.position="relative",s.style.outline="0",s.style.border="0",s.style.margin="0",s.style.padding="0";var t,c,d=document.createElement("div");function h(e){var t,n;document.createRange?((t=document.createRange()).selectNodeContents(e),t.collapse(!1),(n=window.getSelection()).removeAllRanges(),n.addRange(t)):document.selection&&((t=document.body.createTextRange()).moveToElementText(e),t.collapse(!1),t.select())}function a(e){return void 0===t&&((t=document.createElement("span")).style.visibility="hidden",t.style.position="fixed",t.style.outline="0",t.style.margin="0",t.style.padding="0",t.style.border="0",t.style.left="0",t.style.whiteSpace="pre",t.style.fontSize=o,t.style.fontFamily=r,t.style.fontWeight="normal",document.body.appendChild(t)),t.textContent=e,t.getBoundingClientRect().right}d.className="autocomplete dropdown",d.style.position="absolute",d.style.visibility="hidden";var u,p,f,n,m,v={onArrowDown:function(){},onArrowUp:function(){},onEnter:function(){},onTab:function(){},startFrom:0,options:[],element:null,elementHint:null,elementStyle:null,wrapper:s,show:function(e,t,n){var i=this;this.startFrom=t,this.wrapper.remove(),this.elementHint&&(this.elementHint.remove(),this.elementHint=null),""===o&&(o=window.getComputedStyle(e).getPropertyValue("font-size")),""===r&&(r=window.getComputedStyle(e).getPropertyValue("font-family")),d.style.marginLeft="0",d.style.marginTop=e.getBoundingClientRect().height+"px",this.options=n.map(String),this.element!==e&&(this.element=e,this.elementStyle={zIndex:this.element.style.zIndex,position:this.element.style.position,backgroundColor:this.element.style.backgroundColor,borderColor:this.element.style.borderColor}),this.element.style.zIndex=3,this.element.style.position="relative",this.element.style.backgroundColor="transparent",this.element.style.borderColor="transparent",this.elementHint=e.cloneNode(),this.elementHint.className="autocomplete hint",this.elementHint.style.zIndex=2,this.elementHint.style.position="absolute",this.elementHint.onfocus=function(){i.element.focus()},this.element.addEventListener&&(this.element.removeEventListener("keydown",C),this.element.addEventListener("keydown",C,!1),this.element.removeEventListener("blur",_),this.element.addEventListener("blur",_,!1)),s.appendChild(this.elementHint),s.appendChild(d),e.parentElement.appendChild(s),this.repaint(e)},setText:function(e){this.element.innerText=e},getText:function(){return this.element.innerText},hideDropDown:function(){this.wrapper.remove(),this.elementHint&&(this.elementHint.remove(),this.elementHint=null,g.hide(),this.element.style.zIndex=this.elementStyle.zIndex,this.element.style.position=this.elementStyle.position,this.element.style.backgroundColor=this.elementStyle.backgroundColor,this.element.style.borderColor=this.elementStyle.borderColor)},repaint:function(e){var t=(t=e.innerText).replace("\n",""),n=this.options.length,i=t.substring(this.startFrom);c=t.substring(0,this.startFrom);for(var o=0;o<n;o++){var r=this.options[o];if(!l.caseSensitive&&0===r.toLowerCase().indexOf(i.toLowerCase())||l.caseSensitive&&0===r.indexOf(i)){this.elementHint.innerText=c+i+r.substring(i.length),this.elementHint.realInnerText=c+r;break}}d.style.left=a(c)+"px",g.refresh(i,this.options),this.elementHint.style.width=a(this.elementHint.innerText)+10+"px","hidden"===d.style.visibility||(this.elementHint.style.width=a(this.elementHint.innerText)+d.clientWidth+"px")}},g=(u=d,p=[],f=0,n=-1,m={rs:v,hide:function(){u.style.visibility="hidden"},refresh:function(i,e){u.style.visibility="hidden",f=0,u.textContent="";var t=window.innerHeight||document.documentElement.clientHeight,n=u.parentNode.getBoundingClientRect(),o=n.top-6,r=t-n.bottom-6;p=[];var s="function"==typeof l.filter?l.filter:A[l.filter],a=s?e.filter(function(e){return s(l.caseSensitive?i:i.toLowerCase(),l.caseSensitive?e:e.toLowerCase(),l)}):[];0!==(p=a.map(function(e){var t=document.createElement("div");t.className="item",t.onmouseover=y,t.onmouseout=b,t.onmousedown=x,t.__hint=e,t.textContent="",t.appendChild(document.createTextNode(e.substring(0,i.length)));var n=document.createElement("b");return n.appendChild(document.createTextNode(e.substring(i.length))),t.appendChild(n),u.appendChild(t),t})).length&&(1===p.length&&(i.toLowerCase()===p[0].__hint.toLowerCase()&&!l.caseSensitive||i===p[0].__hint&&l.caseSensitive)||p.length<2||(m.highlight(0),3*r<o?(u.style.maxHeight=o+"px",u.style.top="",u.style.bottom="100%"):(u.style.top="100%",u.style.bottom="",u.style.maxHeight=r+"px"),u.style.visibility="visible"))},highlight:function(e){-1!==n&&p[n]&&(p[n].className="item"),p[e].className="item hover",n=e},move:function(e){return"hidden"===u.style.visibility?"":(f+e===-1||f+e===p.length||(f+=e,m.highlight(f)),p[f].__hint)},onmouseselection:function(){}});function y(){this.style.backgroundColor="#ddd"}function b(){this.style.backgroundColor=""}function x(){m.hide(),m.onmouseselection(this.__hint,m.rs)}var C=function(e){var t=(e=e||window.event).keyCode;if(null!=this.elementHint&&33!==t&&34!==t){if(27===t)return v.hideDropDown(),v.element.focus(),e.preventDefault(),void e.stopPropagation();var n,i,o=(o=this.element.innerText).replace("\n","");if(0<=l.confirmKeys.indexOf(t))return 9===t&&0===this.elementHint.innerText.length&&v.onTab(),void(0<this.elementHint.innerText.length&&this.element.innerText!==this.elementHint.realInnerText&&(this.element.innerText=this.elementHint.realInnerText,v.hideDropDown(),h(this.element),9===t&&(v.element.focus(),e.preventDefault(),e.stopPropagation())));if(13!==t){if(40===t){var r=o.substring(this.startFrom),s=g.move(1);return""===s&&v.onArrowDown(),this.elementHint.innerText=c+r+s.substring(r.length),this.elementHint.realInnerText=c+s,e.preventDefault(),void e.stopPropagation()}38===t&&(n=o.substring(this.startFrom),""===(i=g.move(-1))&&v.onArrowUp(),this.elementHint.innerText=c+n+i.substring(n.length),this.elementHint.realInnerText=c+i,e.preventDefault(),e.stopPropagation())}else if(0===this.elementHint.innerText.length)v.onEnter();else{var a="hidden"===d.style.visibility;if(g.hide(),a)return v.hideDropDown(),v.element.focus(),void v.onEnter();this.element.innerText=this.elementHint.realInnerText,v.hideDropDown(),h(this.element),e.preventDefault(),e.stopPropagation()}}}.bind(v),_=function(){v.hideDropDown()};return g.onmouseselection=function(e,t){t.element.innerText=t.elementHint.innerText=c+e,t.hideDropDown(),window.setTimeout(function(){t.element.focus(),h(t.element)},1)},v}(t.autocomplete)),this.options.history&&"view"!==this.options.mode&&(this.history=new l(this)),this._createFrame(),this._createTable()},B.destroy=function(){this.frame&&this.container&&this.frame.parentNode===this.container&&(this.container.removeChild(this.frame),this.frame=null),this.container=null,this.dom=null,this.clear(),this.node=null,this.focusTarget=null,this.selection=null,this.multiselection=null,this.errorNodes=null,this.validateSchema=null,this._debouncedValidate=null,this.history&&(this.history.destroy(),this.history=null),this.searchBox&&(this.searchBox.destroy(),this.searchBox=null),this.modeSwitcher&&(this.modeSwitcher.destroy(),this.modeSwitcher=null),this.frameFocusTracker.destroy()},B._setOptions=function(t){var n=this;this.options={search:!0,history:!0,mode:"tree",name:void 0,schema:null,schemaRefs:null,autocomplete:null,navigationBar:!0,mainMenuBar:!0,limitDragging:!1,onSelectionChange:null,colorPicker:!0,onColorPicker:function(e,t,n){var i,o;r.a?(i=e.getBoundingClientRect().top,o=window.innerHeight-i<300&&300<i,new r.a({parent:e,color:t,popup:o?"top":"bottom",onDone:function(e){var t=1===e.rgba[3]?e.hex.substr(0,7):e.hex;n(t)}}).show()):console.warn("Cannot open color picker: the `vanilla-picker` library is not included in the bundle. Either use the full bundle or implement your own color picker using `onColorPicker`.")},timestampTag:!0,timestampFormat:null,createQuery:V.a,executeQuery:V.b,onEvent:null,enableSort:!0,enableTransform:!0},t&&(Object.keys(t).forEach(function(e){n.options[e]=t[e]}),null==t.limitDragging&&null!=t.schema&&(this.options.limitDragging=!0)),this.setSchema(this.options.schema,this.options.schemaRefs),this._debouncedValidate=Object(P.debounce)(this.validate.bind(this),this.DEBOUNCE_INTERVAL),t.onSelectionChange&&this.onSelectionChange(t.onSelectionChange),Object(h.b)(this.options.languages),Object(h.a)(this.options.language)},B.set=function(e){var t,n;e instanceof Function||void 0===e?this.clear():(this.content.removeChild(this.table),t={field:this.options.name,value:e},n=new S(this,t),this._setRoot(n),this.validate(),this.node.expand(!1),this.content.appendChild(this.table)),this.history&&this.history.clear(),this.searchBox&&this.searchBox.clear()},B.update=function(e){var t,n,i;this.node.deepEqual(e)||(t=this.getSelection(),this.onChangeDisabled=!0,this.node.update(e),this.onChangeDisabled=!1,this.validate(),this.searchBox&&!this.searchBox.isEmpty()&&this.searchBox.forceSearch(),t&&t.start&&t.end?(n=this.node.findNodeByPath(t.start.path),i=this.node.findNodeByPath(t.end.path),n&&i?this.setSelection(t.start,t.end):this.setSelection({},{})):this.setSelection({},{}))},B.get=function(){return this.node?this.node.getValue():void 0},B.getText=function(){return JSON.stringify(this.get())},B.setText=function(t){try{this.set(Object(P.parse)(t))}catch(e){var n=Object(P.repair)(t);this.set(Object(P.parse)(n))}},B.updateText=function(t){try{this.update(Object(P.parse)(t))}catch(e){var n=Object(P.repair)(t);this.update(Object(P.parse)(n))}},B.setName=function(e){this.options.name=e,this.node&&this.node.updateField(this.options.name)},B.getName=function(){return this.options.name},B.focus=function(){var e=this.scrollableContent.querySelector("[contenteditable=true]");e?e.focus():this.node.dom.expand?this.node.dom.expand.focus():this.node.dom.menu?this.node.dom.menu.focus():(e=this.frame.querySelector("button"))&&e.focus()},B.clear=function(){this.node&&(this.node.hide(),delete this.node),this.treePath&&this.treePath.reset()},B._setRoot=function(e){this.clear(),(this.node=e).setParent(null),e.setField(this.getName(),!1),delete e.index,this.tbody.appendChild(e.getDom())},B.search=function(e){var t;return this.node?(this.content.removeChild(this.table),t=this.node.search(e),this.content.appendChild(this.table)):t=[],t},B.expandAll=function(){this.node&&(this.content.removeChild(this.table),this.node.expand(),this.content.appendChild(this.table))},B.collapseAll=function(){this.node&&(this.content.removeChild(this.table),this.node.collapse(),this.content.appendChild(this.table))},B._onAction=function(e,t){this.history&&this.history.add(e,t),this._onChange()},B._onChange=function(){if(!this.onChangeDisabled){var e;if(this.selection=this.getDomSelection(),this._debouncedValidate(),this.treePath&&((e=this.node&&this.selection?this.node.findNodeByInternalPath(this.selection.path):this.multiselection?this.multiselection.nodes[0]:void 0)?this._updateTreePath(e.getNodePath()):this.treePath.reset()),this.options.onChange)try{this.options.onChange()}catch(e){console.error("Error in onChange callback: ",e)}if(this.options.onChangeJSON)try{this.options.onChangeJSON(this.get())}catch(e){console.error("Error in onChangeJSON callback: ",e)}if(this.options.onChangeText)try{this.options.onChangeText(this.getText())}catch(e){console.error("Error in onChangeText callback: ",e)}if(this.options.onClassName&&this.node.recursivelyUpdateCssClassesOnNodes(),this.options.onNodeName&&this.node.childs)try{this.node.recursivelyUpdateNodeName()}catch(e){console.error("Error in onNodeName callback: ",e)}}},B.validate=function(){var n=this,t=this.node;if(t){var e=t.getValue(),i=[];this.validateSchema&&(this.validateSchema(e)||(i=this.validateSchema.errors.map(function(e){return Object(P.improveSchemaError)(e)}).map(function(e){return{node:t.findNode(e.dataPath),error:e,type:"validation"}}).filter(function(e){return null!=e.node})));try{this.validationSequence++;var o=this,r=this.validationSequence;this._validateCustom(e).then(function(e){var t;r===o.validationSequence&&(t=[].concat(i,e||[]),o._renderValidationErrors(t),"function"==typeof n.options.onValidationError&&(Object(P.isValidationErrorChanged)(t,n.lastSchemaErrors)&&n.options.onValidationError.call(n,t),n.lastSchemaErrors=t))}).catch(function(e){console.error(e)})}catch(e){console.error(e)}}},B._renderValidationErrors=function(e){this.errorNodes&&this.errorNodes.forEach(function(e){e.setError(null)});var t=e.reduce(function(e,t){return t.node.findParents().filter(function(t){return!e.some(function(e){return e[0]===t})}).map(function(e){return[e,t.node]}).concat(e)},[]);this.errorNodes=t.map(function(e){return{node:e[0],child:e[1],error:{message:"object"===e[0].type?"Contains invalid properties":"Contains invalid items"}}}).concat(e).map(function(e){return e.node.setError(e.error,e.child),e.node})},B._validateCustom=function(e){try{if(this.options.onValidate){var n=this.node,t=this.options.onValidate(e);return(Object(P.isPromise)(t)?t:Promise.resolve(t)).then(function(e){return Array.isArray(e)?e.filter(function(e){var t=Object(P.isValidValidationError)(e);return t||console.warn('Ignoring a custom validation error with invalid structure. Expected structure: {path: [...], message: "..."}. Actual error:',e),t}).map(function(e){var t;try{t=e&&e.path?n.findNodeByPath(e.path):null}catch(e){}return t||console.warn("Ignoring validation error: node not found. Path:",e.path,"Error:",e),{node:t,error:e,type:"customValidation"}}).filter(function(e){return e&&e.node&&e.error&&e.error.message}):null})}}catch(e){return Promise.reject(e)}return Promise.resolve(null)},B.refresh=function(){this.node&&this.node.updateDom({recurse:!0})},B.startAutoScroll=function(e){var t=this,n=this.scrollableContent,i=Object(P.getAbsoluteTop)(n),o=n.clientHeight,r=i+o;e<i+24&&0<n.scrollTop?this.autoScrollStep=(i+24-e)/3:r-24<e&&o+n.scrollTop<n.scrollHeight?this.autoScrollStep=(r-24-e)/3:this.autoScrollStep=void 0,this.autoScrollStep?this.autoScrollTimer||(this.autoScrollTimer=setInterval(function(){t.autoScrollStep?n.scrollTop-=t.autoScrollStep:t.stopAutoScroll()},50)):this.stopAutoScroll()},B.stopAutoScroll=function(){this.autoScrollTimer&&(clearTimeout(this.autoScrollTimer),delete this.autoScrollTimer),this.autoScrollStep&&delete this.autoScrollStep},B.setDomSelection=function(e){var t,n,i,o,r;e&&("scrollTop"in e&&this.scrollableContent&&(this.scrollableContent.scrollTop=e.scrollTop),e.paths?(t=this,n=e.paths.map(function(e){return t.node.findNodeByInternalPath(e)}),this.select(n)):(o=(i=e.path?this.node.findNodeByInternalPath(e.path):null)&&e.domName?i.dom[e.domName]:null,e.range&&o?(r=Object.assign({},e.range,{container:o}),Object(P.setSelectionOffset)(r)):i&&i.focus()))},B.getDomSelection=function(){var t=S.getNodeFromTarget(this.focusTarget),n=this.focusTarget,e=t?Object.keys(t.dom).find(function(e){return t.dom[e]===n}):null,i=Object(P.getSelectionOffset)();return i&&"DIV"!==i.container.nodeName&&(i=null),i&&i.container!==n&&(i=null),i&&delete i.container,{path:t?t.getInternalPath():null,domName:e,range:i,paths:0<this.multiselection.length?this.multiselection.nodes.map(function(e){return e.getInternalPath()}):null,scrollTop:this.scrollableContent?this.scrollableContent.scrollTop:0}},B.scrollTo=function(e,i){var o,t,n,r,s=this.scrollableContent;s?((o=this).animateTimeout&&(clearTimeout(o.animateTimeout),delete o.animateTimeout),o.animateCallback&&(o.animateCallback(!1),delete o.animateCallback),t=s.clientHeight,n=s.scrollHeight-t,r=Math.min(Math.max(e-t/4,0),n),function e(){var t=s.scrollTop,n=r-t;3<Math.abs(n)?(s.scrollTop+=n/3,o.animateCallback=i,o.animateTimeout=setTimeout(e,50)):(i&&i(!0),s.scrollTop=r,delete o.animateTimeout,delete o.animateCallback)}()):i&&i(!1)},B._createFrame=function(){this.frame=document.createElement("div"),this.frame.className="jsoneditor jsoneditor-mode-"+this.options.mode,this.container.appendChild(this.frame),this.contentOuter=document.createElement("div"),this.contentOuter.className="jsoneditor-outer";var t=this;function n(e){t._onEvent&&t._onEvent(e)}var e,i,o,r,s,a,l,c={target:this.frame,onFocus:this.options.onFocus||null,onBlur:this.options.onBlur||null};this.frameFocusTracker=new D.a(c),this.frame.onclick=function(e){var t=e.target;n(e),"BUTTON"===t.nodeName&&e.preventDefault()},this.frame.oninput=n,this.frame.onchange=n,this.frame.onkeydown=n,this.frame.onkeyup=n,this.frame.oncut=n,this.frame.onpaste=n,this.frame.onmousedown=n,this.frame.onmouseup=n,this.frame.onmouseover=n,this.frame.onmouseout=n,Object(P.addEventListener)(this.frame,"focus",n,!0),Object(P.addEventListener)(this.frame,"blur",n,!0),this.frame.onfocusin=n,this.frame.onfocusout=n,this.options.mainMenuBar&&(Object(P.addClassName)(this.contentOuter,"has-main-menu-bar"),this.menu=document.createElement("div"),this.menu.className="jsoneditor-menu",this.frame.appendChild(this.menu),(e=document.createElement("button")).type="button",e.className="jsoneditor-expand-all",e.title=Object(h.c)("expandAll"),e.onclick=function(){t.expandAll()},this.menu.appendChild(e),(i=document.createElement("button")).type="button",i.title=Object(h.c)("collapseAll"),i.className="jsoneditor-collapse-all",i.onclick=function(){t.collapseAll()},this.menu.appendChild(i),this.options.enableSort&&((o=document.createElement("button")).type="button",o.className="jsoneditor-sort",o.title=Object(h.c)("sortTitleShort"),o.onclick=function(){t.node.showSortModal()},this.menu.appendChild(o)),this.options.enableTransform&&((r=document.createElement("button")).type="button",r.title=Object(h.c)("transformTitleShort"),r.className="jsoneditor-transform",r.onclick=function(){t.node.showTransformModal()},this.menu.appendChild(r)),this.history&&((s=document.createElement("button")).type="button",s.className="jsoneditor-undo jsoneditor-separator",s.title=Object(h.c)("undo"),s.onclick=function(){t._onUndo()},this.menu.appendChild(s),this.dom.undo=s,(a=document.createElement("button")).type="button",a.className="jsoneditor-redo",a.title=Object(h.c)("redo"),a.onclick=function(){t._onRedo()},this.menu.appendChild(a),this.dom.redo=a,this.history.onChange=function(){s.disabled=!t.history.canUndo(),a.disabled=!t.history.canRedo()},this.history.onChange()),this.options&&this.options.modes&&this.options.modes.length&&((l=this).modeSwitcher=new I.a(this.menu,this.options.modes,this.options.mode,function(e){l.setMode(e),l.modeSwitcher.focus()})),this.options.search&&(this.searchBox=new d(this,this.menu))),this.options.navigationBar&&(this.navBar=document.createElement("div"),this.navBar.className="jsoneditor-navigation-bar nav-bar-empty",this.frame.appendChild(this.navBar),this.treePath=new f(this.navBar,this.getPopupAnchor()),this.treePath.onSectionSelected(this._onTreePathSectionSelected.bind(this)),this.treePath.onContextMenuItemSelected(this._onTreePathMenuItemSelected.bind(this)))},B._onUndo=function(){this.history&&(this.history.undo(),this._onChange())},B._onRedo=function(){this.history&&(this.history.redo(),this._onChange())},B._onEvent=function(e){if(!S.targetIsColorPicker(e.target)){var t,n=S.getNodeFromTarget(e.target);if("keydown"===e.type&&this._onKeyDown(e),n&&"focus"===e.type&&(this.focusTarget=e.target,this.options.autocomplete&&"focus"===this.options.autocomplete.trigger&&this._showAutoComplete(e.target)),"mousedown"===e.type&&this._startDragDistance(e),"mousemove"!==e.type&&"mouseup"!==e.type&&"click"!==e.type||this._updateDragDistance(e),n&&this.options&&this.options.navigationBar&&n&&("keydown"===e.type||"mousedown"===e.type)&&(t=this,setTimeout(function(){t._updateTreePath(n.getNodePath())})),n&&n.selected){if("click"===e.type){if(e.target===n.dom.menu)return void this.showContextMenu(e.target);e.hasMoved||this.deselect()}"mousedown"===e.type&&S.onDragStart(this.multiselection.nodes,e)}else"mousedown"===e.type&&Object(P.hasParentNode)(e.target,this.content)&&(this.deselect(),n&&e.target===n.dom.drag?S.onDragStart(n,e):n&&(e.target===n.dom.field||e.target===n.dom.value||e.target===n.dom.select)||this._onMultiSelectStart(e));n&&n.onEvent(e)}},B._updateTreePath=function(e){var n;function i(e){return e.parent?"array"===e.parent.type?e.index:e.field:e.field||e.type}e&&e.length?(Object(P.removeClassName)(this.navBar,"nav-bar-empty"),n=[],e.forEach(function(e){var t={name:i(e),node:e,children:[]};e.childs&&e.childs.length&&e.childs.forEach(function(e){t.children.push({name:i(e),node:e})}),n.push(t)}),this.treePath.setPath(n)):Object(P.addClassName)(this.navBar,"nav-bar-empty")},B._onTreePathSectionSelected=function(e){e&&e.node&&(e.node.expandTo(),e.node.focus())},B._onTreePathMenuItemSelected=function(e,t){var n;e&&e.children.length&&((n=e.children.find(function(e){return e.name===t}))&&n.node&&(this._updateTreePath(n.node.getNodePath()),n.node.expandTo(),n.node.focus()))},B._startDragDistance=function(e){this.dragDistanceEvent={initialTarget:e.target,initialPageX:e.pageX,initialPageY:e.pageY,dragDistance:0,hasMoved:!1}},B._updateDragDistance=function(e){this.dragDistanceEvent||this._startDragDistance(e);var t=e.pageX-this.dragDistanceEvent.initialPageX,n=e.pageY-this.dragDistanceEvent.initialPageY;return this.dragDistanceEvent.dragDistance=Math.sqrt(t*t+n*n),this.dragDistanceEvent.hasMoved=this.dragDistanceEvent.hasMoved||10<this.dragDistanceEvent.dragDistance,e.dragDistance=this.dragDistanceEvent.dragDistance,e.hasMoved=this.dragDistanceEvent.hasMoved,e.dragDistance},B._onMultiSelectStart=function(e){var t,n=S.getNodeFromTarget(e.target);"tree"===this.options.mode&&void 0===this.options.onEditable&&(this.multiselection={start:n||null,end:null,nodes:[]},this._startDragDistance(e),(t=this).mousemove||(this.mousemove=Object(P.addEventListener)(window,"mousemove",function(e){t._onMultiSelect(e)})),this.mouseup||(this.mouseup=Object(P.addEventListener)(window,"mouseup",function(e){t._onMultiSelectEnd(e)})),e.preventDefault())},B._onMultiSelect=function(e){var t,n,i,o;e.preventDefault(),this._updateDragDistance(e),e.hasMoved&&((t=S.getNodeFromTarget(e.target))&&(null==this.multiselection.start&&(this.multiselection.start=t),this.multiselection.end=t),this.deselect(),n=this.multiselection.start,i=this.multiselection.end||this.multiselection.start,n&&i&&(this.multiselection.nodes=this._findTopLevelNodes(n,i),this.multiselection.nodes&&this.multiselection.nodes.length&&(o=this.multiselection.nodes[0],this.multiselection.start===o||this.multiselection.start.isDescendantOf(o)?this.multiselection.direction="down":this.multiselection.direction="up"),this.select(this.multiselection.nodes)))},B._onMultiSelectEnd=function(){this.multiselection.nodes[0]&&this.multiselection.nodes[0].dom.menu.focus(),this.multiselection.start=null,this.multiselection.end=null,this.mousemove&&(Object(P.removeEventListener)(window,"mousemove",this.mousemove),delete this.mousemove),this.mouseup&&(Object(P.removeEventListener)(window,"mouseup",this.mouseup),delete this.mouseup)},B.deselect=function(e){var t=!!this.multiselection.nodes.length;this.multiselection.nodes.forEach(function(e){e.setSelected(!1)}),this.multiselection.nodes=[],e&&(this.multiselection.start=null,this.multiselection.end=null),t&&this._selectionChangedHandler&&this._selectionChangedHandler()},B.select=function(e){if(!Array.isArray(e))return this.select([e]);var t,n;e&&(this.deselect(),this.multiselection.nodes=e.slice(0),t=e[0],e.forEach(function(e){e.expandPathToNode(),e.setSelected(!0,e===t)}),this._selectionChangedHandler&&(n=this.getSelection(),this._selectionChangedHandler(n.start,n.end)))},B._findTopLevelNodes=function(e,t){for(var n=e.getNodePath(),i=t.getNodePath(),o=0;o<n.length&&n[o]===i[o];)o++;var r=n[o-1],s=n[o],a=i[o];if(s&&a||(r.parent?r=(a=s=r).parent:(s=r.childs[0],a=r.childs[r.childs.length-1])),r&&s&&a){var l=r.childs.indexOf(s),c=r.childs.indexOf(a),d=Math.min(l,c),h=Math.max(l,c);return r.childs.slice(d,h+1)}return[]},B._showAutoComplete=function(t){var n=S.getNodeFromTarget(t),i="";0<=t.className.indexOf("jsoneditor-value")&&(i="value"),0<=t.className.indexOf("jsoneditor-field")&&(i="field");var o=this;setTimeout(function(){var e;!n||!("focus"===o.options.autocomplete.trigger||0<t.innerText.length)||null===(e=o.options.autocomplete.getOptions(t.innerText,n.getPath(),i,n.editor))?o.autocomplete.hideDropDown():"function"==typeof e.then?e.then(function(e){null===e?o.autocomplete.hideDropDown():e.options?o.autocomplete.show(t,e.startFrom,e.options):o.autocomplete.show(t,0,e)}).catch(function(e){console.error(e)}):e.options?o.autocomplete.show(t,e.startFrom,e.options):o.autocomplete.show(t,0,e)},50)},B._onKeyDown=function(e){var t,n=e.which||e.keyCode,i=e.altKey,o=e.ctrlKey,r=e.metaKey,s=e.shiftKey,a=!1,l=this.focusTarget;9===n&&(t=this,setTimeout(function(){t.focusTarget!==l&&Object(P.selectContentEditable)(t.focusTarget)},0)),this.searchBox&&(o&&70===n?(this.searchBox.dom.search.focus(),this.searchBox.dom.search.select(),a=!0):(114===n||o&&71===n)&&(s?this.searchBox.previous(!0):this.searchBox.next(!0),a=!0)),this.history&&(o&&!s&&90===n?(this._onUndo(),a=!0):o&&s&&90===n&&(this._onRedo(),a=!0)),this.options.autocomplete&&!a&&(
o||i||r||1!==e.key.length&&8!==n&&46!==n||(a=!1,this._showAutoComplete(e.target))),a&&(e.preventDefault(),e.stopPropagation())},B._createTable=function(){var e;this.options.navigationBar&&Object(P.addClassName)(this.contentOuter,"has-nav-bar"),this.scrollableContent=document.createElement("div"),this.scrollableContent.className="jsoneditor-tree",this.contentOuter.appendChild(this.scrollableContent),this.content=document.createElement("div"),this.content.className="jsoneditor-tree-inner",this.scrollableContent.appendChild(this.content),this.table=document.createElement("table"),this.table.className="jsoneditor-tree",this.content.appendChild(this.table),this.colgroupContent=document.createElement("colgroup"),"tree"===this.options.mode&&((e=document.createElement("col")).width="24px",this.colgroupContent.appendChild(e)),(e=document.createElement("col")).width="24px",this.colgroupContent.appendChild(e),e=document.createElement("col"),this.colgroupContent.appendChild(e),this.table.appendChild(this.colgroupContent),this.tbody=document.createElement("tbody"),this.table.appendChild(this.tbody),this.frame.appendChild(this.contentOuter)},B.showContextMenu=function(e,t){var n,i=[],o=this.multiselection.nodes.slice();i.push({text:Object(h.c)("duplicateText"),title:Object(h.c)("duplicateTitle"),className:"jsoneditor-duplicate",click:function(){S.onDuplicate(o)}}),i.push({text:Object(h.c)("remove"),title:Object(h.c)("removeTitle"),className:"jsoneditor-remove",click:function(){S.onRemove(o)}}),this.options.onCreateMenu&&(n=o.map(function(e){return e.getPath()}),i=this.options.onCreateMenu(i,{type:"multiple",path:n[0],paths:n})),new u.a(i,{close:t}).show(e,this.getPopupAnchor())},B.getPopupAnchor=function(){return this.options.popupAnchor||this.frame},B.getSelection=function(){var e,t,n={start:null,end:null};return this.multiselection.nodes&&this.multiselection.nodes.length&&this.multiselection.nodes.length&&(e=this.multiselection.nodes[0],t=this.multiselection.nodes[this.multiselection.nodes.length-1],"down"===this.multiselection.direction?(n.start=e.serialize(),n.end=t.serialize()):(n.start=t.serialize(),n.end=e.serialize())),n},B.onSelectionChange=function(e){"function"==typeof e&&(this._selectionChangedHandler=Object(P.debounce)(e,this.DEBOUNCE_INTERVAL))},B.setSelection=function(e,t){e&&e.dom&&e.range&&(console.warn("setSelection/getSelection usage for text selection is deprecated and should not be used, see documentation for supported selection options"),this.setDomSelection(e));var n=this._getNodeInstancesByRange(e,t);n.forEach(function(e){e.expandTo()}),this.select(n)},B._getNodeInstancesByRange=function(e,t){var n,i;e&&e.path&&(n=this.node.findNodeByPath(e.path),t&&t.path&&(i=this.node.findNodeByPath(t.path)));var o=[];if(n instanceof S)if(i instanceof S&&i!==n)if(n.parent===i.parent){t=n.getIndex()<i.getIndex()?(e=n,i):(e=i,n);var r=e;for(o.push(r);r=r.nextSibling(),o.push(r),r&&r!==t;);}else o=this._findTopLevelNodes(n,i);else o.push(n);return o},B.getNodesByRange=function(e,t){var n=this._getNodeInstancesByRange(e,t),i=[];return n.forEach(function(e){i.push(e.serialize())}),i};var L=[{mode:"tree",mixin:B,data:"json"},{mode:"view",mixin:B,data:"json"},{mode:"form",mixin:B,data:"json"}]},function(e,t,n){"use strict";n.r(t),n.d(t,"previewModeMixins",function(){return c});var u=n(1),p=n(7),f=n(14),i=n(5),a=n(6),o=n(16),m=n(2),v=n(8),g=n(0);function r(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var y=function(){function i(e,t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),this.onChange=e,this.calculateItemSize=t||function(){return 1},this.limit=n,this.items=[],this.index=-1}var e,t,n;return e=i,(t=[{key:"add",value:function(e){for(;this._calculateHistorySize()>this.limit&&1<this.items.length;)this.items.shift(),this.index--;this.items=this.items.slice(0,this.index+1),this.items.push(e),this.index++,this.onChange()}},{key:"_calculateHistorySize",value:function(){var t=this.calculateItemSize,n=0;return this.items.forEach(function(e){n+=t(e)}),n}},{key:"undo",value:function(){if(this.canUndo())return this.index--,this.onChange(),this.items[this.index]}},{key:"redo",value:function(){if(this.canRedo())return this.index++,this.onChange(),this.items[this.index]}},{key:"canUndo",value:function(){return 0<this.index}},{key:"canRedo",value:function(){return this.index<this.items.length-1}},{key:"clear",value:function(){this.items=[],this.index=-1,this.onChange()}}])&&r(e.prototype,t),n&&r(e,n),i}(),b=n(4),s=o.textModeMixins[0].mixin,l={create:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};void 0===t.statusBar&&(t.statusBar=!0),t.mainMenuBar=!1!==t.mainMenuBar,t.enableSort=!1!==t.enableSort,t.enableTransform=!1!==t.enableTransform,t.createQuery=t.createQuery||b.a,t.executeQuery=t.executeQuery||b.b,"number"==typeof(this.options=t).indentation?this.indentation=Number(t.indentation):this.indentation=2,Object(u.b)(this.options.languages),Object(u.a)(this.options.language),this.mode="preview";var n=this;this.container=e,this.dom={},this.json=void 0,this.text="",this._debouncedValidate=Object(g.debounce)(this.validate.bind(this),this.DEBOUNCE_INTERVAL),this.width=e.clientWidth,this.height=e.clientHeight,this.frame=document.createElement("div"),this.frame.className="jsoneditor jsoneditor-mode-preview",this.frame.onclick=function(e){e.preventDefault()};var i,o,r,s,a,l,c,d,h={target:this.frame,onFocus:this.options.onFocus||null,onBlur:this.options.onBlur||null};this.frameFocusTracker=new v.a(h),this.content=document.createElement("div"),this.content.className="jsoneditor-outer",this.dom.busy=document.createElement("div"),this.dom.busy.className="jsoneditor-busy",this.dom.busyContent=document.createElement("span"),this.dom.busyContent.textContent="busy...",this.dom.busy.appendChild(this.dom.busyContent),this.content.appendChild(this.dom.busy),this.dom.previewContent=document.createElement("pre"),this.dom.previewContent.className="jsoneditor-preview",this.dom.previewText=document.createTextNode(""),this.dom.previewContent.appendChild(this.dom.previewText),this.content.appendChild(this.dom.previewContent),this.options.mainMenuBar&&(Object(g.addClassName)(this.content,"has-main-menu-bar"),this.menu=document.createElement("div"),this.menu.className="jsoneditor-menu",this.frame.appendChild(this.menu),(i=document.createElement("button")).type="button",i.className="jsoneditor-format",i.title=Object(u.c)("formatTitle"),this.menu.appendChild(i),i.onclick=function(){n.executeWithBusyMessage(function(){try{n.format()}catch(e){n._onError(e)}},"formatting...")},(o=document.createElement("button")).type="button",o.className="jsoneditor-compact",o.title=Object(u.c)("compactTitle"),this.menu.appendChild(o),o.onclick=function(){n.executeWithBusyMessage(function(){try{n.compact()}catch(e){n._onError(e)}},"compacting...")},this.options.enableSort&&((r=document.createElement("button")).type="button",r.className="jsoneditor-sort",r.title=Object(u.c)("sortTitleShort"),r.onclick=function(){n._showSortModal()},this.menu.appendChild(r)),this.options.enableTransform&&((s=document.createElement("button")).type="button",s.title=Object(u.c)("transformTitleShort"),s.className="jsoneditor-transform",s.onclick=function(){n._showTransformModal()},this.dom.transform=s,this.menu.appendChild(s)),(a=document.createElement("button")).type="button",a.className="jsoneditor-repair",a.title=Object(u.c)("repairTitle"),this.menu.appendChild(a),!(a.onclick=function(){void 0===n.json&&n.executeWithBusyMessage(function(){try{n.repair()}catch(e){n._onError(e)}},"repairing...")})!==this.options.history&&(this.history=new y(function(){n.dom.undo.disabled=!n.history.canUndo(),n.dom.redo.disabled=!n.history.canRedo()},function(e){return 2*e.text.length},m.c),(l=document.createElement("button")).type="button",l.className="jsoneditor-undo jsoneditor-separator",l.title=Object(u.c)("undo"),l.onclick=function(){var e=n.history.undo();e&&n._applyHistory(e)},this.menu.appendChild(l),this.dom.undo=l,(c=document.createElement("button")).type="button",c.className="jsoneditor-redo",c.title=Object(u.c)("redo"),c.onclick=function(){var e=n.history.redo();e&&n._applyHistory(e)},this.menu.appendChild(c),this.dom.redo=c,this.history.onChange()),this.options&&this.options.modes&&this.options.modes.length&&(this.modeSwitcher=new p.a(this.menu,this.options.modes,this.options.mode,function(e){n.setMode(e),n.modeSwitcher.focus()}))),this.errorTable=new f.a({errorTableVisible:!0,onToggleVisibility:function(){n.validate()},onFocusLine:null,onChangeHeight:function(e){var t=e+(n.dom.statusBar?n.dom.statusBar.clientHeight:0)+1;n.content.style.marginBottom=-t+"px",n.content.style.paddingBottom=t+"px"}}),this.frame.appendChild(this.content),this.frame.appendChild(this.errorTable.getErrorTable()),this.container.appendChild(this.frame),t.statusBar&&(Object(g.addClassName)(this.content,"has-status-bar"),d=document.createElement("div"),(this.dom.statusBar=d).className="jsoneditor-statusbar",this.frame.appendChild(d),this.dom.fileSizeInfo=document.createElement("span"),this.dom.fileSizeInfo.className="jsoneditor-size-info",this.dom.fileSizeInfo.innerText="",d.appendChild(this.dom.fileSizeInfo),this.dom.arrayInfo=document.createElement("span"),this.dom.arrayInfo.className="jsoneditor-size-info",this.dom.arrayInfo.innerText="",d.appendChild(this.dom.arrayInfo),d.appendChild(this.errorTable.getErrorCounter()),d.appendChild(this.errorTable.getWarningIcon()),d.appendChild(this.errorTable.getErrorIcon())),this._renderPreview(),this.setSchema(this.options.schema,this.options.schemaRefs)},_renderPreview:function(){var e=this.getText();this.dom.previewText.nodeValue=Object(g.limitCharacters)(e,m.b),this.dom.fileSizeInfo&&(this.dom.fileSizeInfo.innerText="Size: "+Object(g.formatSize)(e.length)),this.dom.arrayInfo&&(Array.isArray(this.json)?this.dom.arrayInfo.innerText="Array: "+this.json.length+" items":this.dom.arrayInfo.innerText="")},_onChange:function(){if(this._debouncedValidate(),this.options.onChange)try{this.options.onChange()}catch(e){console.error("Error in onChange callback: ",e)}if(this.options.onChangeJSON)try{this.options.onChangeJSON(this.get())}catch(e){console.error("Error in onChangeJSON callback: ",e)}if(this.options.onChangeText)try{this.options.onChangeText(this.getText())}catch(e){console.error("Error in onChangeText callback: ",e)}}};l._showSortModal=function(){var s=this;this.executeWithBusyMessage(function(){var e=s.options.modalAnchor||m.a,r=s.get();s._renderPreview(),Object(i.showSortModal)(e,r,function(o){s.executeWithBusyMessage(function(){var e,t,n,i;e=r,t=o,Array.isArray(e)&&(n=Object(g.sort)(e,t.path,t.direction),s.sortedBy=t,s._setAndFireOnChange(n)),Object(g.isObject)(e)&&(i=Object(g.sortObjectKeys)(e,t.direction),s.sortedBy=t,s._setAndFireOnChange(i))},"sorting...")},s.sortedBy)},"parsing...")},l._showTransformModal=function(){var s=this;this.executeWithBusyMessage(function(){var e=s.options,t=e.createQuery,n=e.executeQuery,i=e.modalAnchor,o=e.queryDescription,r=s.get();s._renderPreview(),Object(a.showTransformModal)({anchor:i||m.a,json:r,queryDescription:o,createQuery:t,executeQuery:n,onTransform:function(t){s.executeWithBusyMessage(function(){var e=n(r,t);s._setAndFireOnChange(e)},"transforming...")}})},"parsing...")},l.destroy=function(){this.frame&&this.container&&this.frame.parentNode===this.container&&this.container.removeChild(this.frame),this.modeSwitcher&&(this.modeSwitcher.destroy(),this.modeSwitcher=null),this._debouncedValidate=null,this.history&&(this.history.clear(),this.history=null),this.frameFocusTracker.destroy()},l.compact=function(){var e=this.get(),t=JSON.stringify(e);this._setTextAndFireOnChange(t,e)},l.format=function(){var e=this.get(),t=JSON.stringify(e,null,this.indentation);this._setTextAndFireOnChange(t,e)},l.repair=function(){var e=this.getText(),t=Object(g.repair)(e);this._setTextAndFireOnChange(t)},l.focus=function(){this.dom.transform.focus()},l.set=function(e){this.history&&this.history.clear(),this._set(e)},l.update=function(e){this._set(e)},l._set=function(e){this.text=void 0,this.json=e,this._renderPreview(),this._pushHistory(),this._debouncedValidate()},l._setAndFireOnChange=function(e){this._set(e),this._onChange()},l.get=function(){var e;return void 0===this.json&&(e=this.getText(),this.json=Object(g.parse)(e)),this.json},l.getText=function(){return void 0===this.text&&(this.text=JSON.stringify(this.json,null,this.indentation),!0===this.options.escapeUnicode&&(this.text=Object(g.escapeUnicodeChars)(this.text))),this.text},l.setText=function(e){this.history&&this.history.clear(),this._setText(e)},l.updateText=function(e){this.getText()!==e&&this._setText(e)},l._setText=function(e,t){var n;!0===this.options.escapeUnicode?this.text=Object(g.escapeUnicodeChars)(e):this.text=e,this.json=t,this._renderPreview(),void 0===this.json?(n=this).executeWithBusyMessage(function(){try{n.json=n.get(),n._renderPreview(),n._pushHistory()}catch(e){}},"parsing..."):this._pushHistory(),this._debouncedValidate()},l._setTextAndFireOnChange=function(e,t){this._setText(e,t),this._onChange()},l._applyHistory=function(e){this.json=e.json,this.text=e.text,this._renderPreview(),this._debouncedValidate()},l._pushHistory=function(){var e;this.history&&(e={text:this.text,json:this.json},this.history.add(e))},l.executeWithBusyMessage=function(e,t){var n;this.getText().length>m.d?(n=this,Object(g.addClassName)(n.frame,"busy"),n.dom.busyContent.innerText=t,setTimeout(function(){e(),Object(g.removeClassName)(n.frame,"busy"),n.dom.busyContent.innerText=""},100)):e()},l.validate=s.validate,l._renderErrors=s._renderErrors;var c=[{mode:"preview",mixin:l,data:"json"}]}],o.c=i,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)o.d(n,i,function(e){return t[e]}.bind(null,i));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=22);function o(e){if(i[e])return i[e].exports;var t=i[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,o),t.l=!0,t.exports}var n,i});
//# sourceMappingURL=jsoneditor-minimalist.map